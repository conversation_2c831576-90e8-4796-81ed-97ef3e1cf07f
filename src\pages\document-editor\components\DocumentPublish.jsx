import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSidebar } from '../../../contexts/SidebarContext';
import { useAuth } from '../../../contexts/AuthContext';
import QuickActionSidebar from '../../../components/ui/QuickActionSidebar';
import DocumentWorkflowHeader from './DocumentWorkflowHeader';
import ExportSuccessModal from './ExportSuccessModal';
import Icon from '../../../components/AppIcon';
import { exportDocument, getDocumentStatistics } from '../../../services/exportService';
import { migrateDocumentIfNeeded } from '../utils/imageMigration';
import { handlePhaseTransition } from '../../../utils/progressUtils';

/**
 * DocumentPublish - Publish phase component for document workflow
 * Provides export functionality for PDF, ePub, DOCX, and HTML formats
 */
const DocumentPublish = () => {
  const { documentId } = useParams();
  const navigate = useNavigate();
  const { isCollapsed, contentMargin } = useSidebar();
  const { user, profile } = useAuth();
  const [documentData, setDocumentData] = useState(null);
  const [generatedContent, setGeneratedContent] = useState(null);
  const [selectedFormat, setSelectedFormat] = useState('pdf');
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);

  // Modal state
  const [showExportModal, setShowExportModal] = useState(false);
  const [exportResult, setExportResult] = useState(null);
  const [exportError, setExportError] = useState(null);



  // Helper function to extract document title from questionnaire data
  const getDocumentTitle = () => {
    if (!documentData) return 'Untitled Document';

    // Check for title in various possible locations
    if (documentData.titleSelection?.selectedTitle && documentData.titleSelection.selectedTitle !== 'custom') {
      return documentData.titleSelection.selectedTitle;
    }
    if (documentData.titleSelection?.customTitle) {
      return documentData.titleSelection.customTitle;
    }
    if (documentData.title) {
      return documentData.title;
    }
    if (generatedContent?.title) {
      return generatedContent.title;
    }

    // Fallback: generate title from topic
    if (documentData.topicAndNiche?.mainTopic) {
      return `Guide to ${documentData.topicAndNiche.mainTopic}`;
    }

    return 'Untitled Document';
  };

  // Helper function to get document author
  const getDocumentAuthor = () => {
    // First check if author is already set in document data
    if (documentData?.author) {
      return documentData.author;
    }

    // Use authenticated user's name
    if (profile?.full_name) {
      return profile.full_name;
    }
    if (profile?.first_name && profile?.last_name) {
      return `${profile.first_name} ${profile.last_name}`;
    }
    if (profile?.first_name) {
      return profile.first_name;
    }
    if (user?.email) {
      return user.email.split('@')[0]; // Use email username as fallback
    }

    return 'Unknown Author';
  };

  // Helper function to get document description
  const getDocumentDescription = () => {
    if (documentData?.description) {
      return documentData.description;
    }

    // Generate description from questionnaire data
    const topic = documentData?.topicAndNiche?.mainTopic;
    const audience = documentData?.audienceAnalysis?.primaryAudience;
    const type = documentData?.documentPurpose?.primaryType;

    if (topic && audience && type) {
      return `A comprehensive ${type} about ${topic} for ${audience}.`;
    }
    if (topic && type) {
      return `A ${type} covering ${topic}.`;
    }
    if (topic) {
      return `A guide covering ${topic}.`;
    }

    return '';
  };

  // Load document data on component mount
  useEffect(() => {
    const loadDocumentData = () => {
      try {
        const savedDocument = localStorage.getItem(`document-${documentId}`);
        if (savedDocument) {
          const parsedDocument = JSON.parse(savedDocument);

          // Migrate old placedImages to image blocks if needed
          const migratedDocument = migrateDocumentIfNeeded(parsedDocument);

          // Save migrated data back to localStorage if migration occurred
          if (migratedDocument.migrated) {
            localStorage.setItem(`document-${documentId}`, JSON.stringify(migratedDocument));
            console.log('✅ Document migrated to block-based images for export');
          }

          setDocumentData(migratedDocument);
          setGeneratedContent(migratedDocument.generatedContent);
        } else {
          console.error('Document not found');
          navigate('/dashboard');
        }
      } catch (error) {
        console.error('Error loading document:', error);
        navigate('/dashboard');
      }
    };

    if (documentId) {
      loadDocumentData();

      // Update progress to Publish phase when component loads
      handlePhaseTransition(documentId, 'Publish', 'Review')
        .then(() => {
          console.log('Progress updated to Publish phase (100%)');
        })
        .catch(error => {
          console.warn('Failed to update progress to Publish phase:', error);
        });
    }
  }, [documentId, navigate]);

  // Export format options - 4 core formats as per user preference
  const exportFormats = [
    {
      id: 'pdf',
      name: 'PDF',
      description: 'For adobe reader',
      icon: 'FileText',
      color: 'text-red-500',
      bgColor: 'bg-red-50',
      primary: true
    },
    {
      id: 'epub',
      name: 'EPUB',
      description: 'E-book format',
      icon: 'Book',
      color: 'text-green-500',
      bgColor: 'bg-green-50'
    },
    {
      id: 'docx',
      name: 'DOCX',
      description: 'Microsoft Word document',
      icon: 'FileText',
      color: 'text-blue-500',
      bgColor: 'bg-blue-50'
    },
    {
      id: 'html',
      name: 'HTML',
      description: 'Web page format',
      icon: 'Code',
      color: 'text-purple-500',
      bgColor: 'bg-purple-50'
    }
  ];

  // Handle phase navigation
  const handlePhaseNavigation = (phase) => {
    switch (phase) {
      case 'Generate':
        // Clear any cached AI-generated content before navigating back to wizard
        console.log('Clearing cached AI content before returning to Generate phase');

        // Clear cached AI-generated content from localStorage
        const aiContentKeys = [
          'titleSelection.generatedTitles',
          'topicAndNiche.availableSubNiches',
          'documentOutline.generatedOutline'
        ];

        aiContentKeys.forEach(key => {
          const storageKey = `docforge_cached_${key}`;
          localStorage.removeItem(storageKey);
        });

        // Navigate back to document creator with reset flag
        navigate('/document-creator', {
          state: { resetAIContent: true }
        });
        break;
      case 'Edit Content':
        navigate(`/document-editor/${documentId}`);
        break;
      case 'Review':
        // Update progress before navigating to review phase
        handlePhaseTransition(documentId, 'Review', 'Publish')
          .then(() => {
            console.log('Progress updated for Review phase from Publish');
            navigate(`/document-editor/${documentId}/review`);
          })
          .catch(error => {
            console.warn('Failed to update progress for Review phase:', error);
            // Still navigate even if progress update fails
            navigate(`/document-editor/${documentId}/review`);
          });
        break;
      case 'Publish':
        // Already on publish page
        break;
      default:
        console.warn(`Unknown phase: ${phase}`);
    }
  };

  // Handle export format selection
  const handleFormatSelection = (format) => {
    setSelectedFormat(format.id);
  };

  // Handle export process
  const handleExport = async () => {
    if (!generatedContent || !documentData) {
      console.error('No document content to export');
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      // Show progress
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Prepare document data with proper title and author for export
      const exportDocumentData = {
        ...documentData,
        title: getDocumentTitle(),
        author: getDocumentAuthor(),
        description: getDocumentDescription()
      };

      // Perform actual export
      const result = await exportDocument(selectedFormat, exportDocumentData, generatedContent);

      clearInterval(progressInterval);
      setExportProgress(100);

      // Show result
      setTimeout(async () => {
        setIsExporting(false);
        setExportProgress(0);

        if (result.success) {
          setExportResult(result);
          setExportError(null);

          // Document is successfully published - no additional storage cleanup needed
          // Database already contains the final version
          console.log('Document successfully published and exported');
        } else {
          setExportError(result.error || 'Export failed');
          setExportResult(null);
        }
        setShowExportModal(true);
      }, 500);

    } catch (error) {
      console.error('Export failed:', error);
      setIsExporting(false);
      setExportProgress(0);
      setExportError(error.message || 'Export failed. Please try again.');
      setExportResult(null);
      setShowExportModal(true);
    }
  };

  // Calculate document statistics using the export service
  const stats = getDocumentStatistics(generatedContent);

  // Modal handlers
  const handleCloseModal = () => {
    setShowExportModal(false);
    setExportResult(null);
    setExportError(null);
  };

  const handleRetryExport = () => {
    setShowExportModal(false);
    setExportError(null);
    handleExport();
  };

  const handleExportAnother = () => {
    setShowExportModal(false);
    setExportResult(null);
    // Keep the current format selection for user to choose another
  };

  const handleDownload = () => {
    // The download should have already been triggered by the export service
    // This is just for user feedback
    setShowExportModal(false);
  };

  if (!documentData || !generatedContent) {
    return (
      <div className="min-h-screen bg-background">
        <QuickActionSidebar />
        <main className={`${contentMargin} ml-0`}>
          <DocumentWorkflowHeader
            currentPhase="Publish"
            onPhaseClick={handlePhaseNavigation}
          />
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600">Loading document...</p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <QuickActionSidebar />
      
      <main className={`${contentMargin} ml-0`}>
        {/* Document Workflow Header */}
        <DocumentWorkflowHeader
          currentPhase="Publish"
          onPhaseClick={handlePhaseNavigation}
        />

        {/* Publish Content */}
        <div className="max-w-6xl mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            {/* Left Column - Export Options */}
            <div className="lg:col-span-2">
              {/* Document Info */}
              <div className="mb-8">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  {getDocumentTitle()}
                </h1>
                <p className="text-gray-600 mb-4">
                  Author: {getDocumentAuthor()}
                </p>
                
                {/* Document Statistics */}
                <div className="flex items-center space-x-6 text-sm text-gray-500">
                  <div className="flex items-center space-x-1">
                    <Icon name="FileText" size={16} />
                    <span>{stats.pages} Pages</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Icon name="List" size={16} />
                    <span>{stats.chapters} Chapters</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Icon name="Type" size={16} />
                    <span>{stats.words.toLocaleString()} Words</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Icon name="Clock" size={16} />
                    <span>{stats.readTime} Read time</span>
                  </div>
                </div>
              </div>

              {/* Export Format Selection */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  How would you like to publish?
                </h2>
                
                <div className="space-y-3">
                  {exportFormats.map((format) => (
                    <div
                      key={format.id}
                      onClick={() => handleFormatSelection(format)}
                      className={`
                        relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200
                        ${selectedFormat === format.id
                          ? 'border-primary bg-primary/5'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }
                      `}
                    >
                      <div className="flex items-center space-x-4">
                        {/* Format Icon */}
                        <div className={`w-12 h-12 rounded-lg ${format.bgColor} flex items-center justify-center`}>
                          <Icon name={format.icon} size={20} className={format.color} />
                        </div>
                        
                        {/* Format Info */}
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-medium text-gray-900">{format.name}</h3>
                            {format.badge && (
                              <span className="px-2 py-0.5 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">
                                {format.badge}
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">{format.description}</p>
                        </div>
                        
                        {/* Selection Radio */}
                        <div className={`
                          w-5 h-5 rounded-full border-2 flex items-center justify-center
                          ${selectedFormat === format.id
                            ? 'border-primary bg-primary'
                            : 'border-gray-300'
                          }
                        `}>
                          {selectedFormat === format.id && (
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Column - Export Settings & Actions */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg border border-gray-200 p-6 sticky top-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Export Settings</h3>

                {/* Document Title Input */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Title
                  </label>
                  <input
                    type="text"
                    value={getDocumentTitle()}
                    onChange={(e) => setDocumentData(prev => ({ ...prev, title: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="Enter document title"
                  />
                </div>

                {/* Author Input */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Author
                  </label>
                  <input
                    type="text"
                    value={getDocumentAuthor()}
                    onChange={(e) => setDocumentData(prev => ({ ...prev, author: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="Enter author name"
                  />
                </div>

                {/* Description Input */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={getDocumentDescription()}
                    onChange={(e) => setDocumentData(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                    placeholder="Enter document description"
                  />
                </div>

                {/* Compress PDF Option (only for PDF) */}
                {selectedFormat === 'pdf' && (
                  <div className="mb-6">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                        defaultChecked
                      />
                      <span className="text-sm text-gray-700">Compress PDF</span>
                    </label>
                  </div>
                )}

                {/* Export Button */}
                <button
                  onClick={handleExport}
                  disabled={isExporting}
                  className={`
                    w-full py-3 px-4 rounded-lg font-medium transition-all duration-200
                    ${isExporting
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-primary hover:bg-primary-dark text-white shadow-sm hover:shadow-md'
                    }
                  `}
                >
                  {isExporting ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Exporting... {exportProgress}%</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center space-x-2">
                      <Icon name="Download" size={16} />
                      <span>Export Document</span>
                    </div>
                  )}
                </button>

                {/* Edit Document Button */}
                <button
                  onClick={() => navigate(`/document-editor/${documentId}`)}
                  className="w-full mt-3 py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Edit Document
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Export Success/Error Modal */}
      <ExportSuccessModal
        isOpen={showExportModal}
        onClose={handleCloseModal}
        isSuccess={!!exportResult && !exportError}
        documentTitle={getDocumentTitle()}
        exportFormat={selectedFormat}
        message={exportResult?.message || exportError}
        onRetry={handleRetryExport}
        onExportAnother={handleExportAnother}
        onDownload={exportResult ? handleDownload : null}
      />
    </div>
  );
};

export default DocumentPublish;
