import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSidebar } from '../../../contexts/SidebarContext';
import { useAuth } from '../../../contexts/AuthContext';
import QuickActionSidebar from '../../../components/ui/QuickActionSidebar';
import DocumentWorkflowHeader from './DocumentWorkflowHeader';
import DocumentInfoHeader from './DocumentInfoHeader';
import DocumentCanvasMinimal from './DocumentCanvasMinimal';
import ValidationResults from './ValidationResults';
import DocumentStatistics from './DocumentStatistics';
import useReviewMode from '../hooks/useReviewMode';
import { validateDocument } from '../../../services/documentValidationService';
import Icon from '../../../components/AppIcon';
import { handlePhaseTransition } from '../../../utils/progressUtils';
import { documentStorage } from '../../../services/documentStorageService';

/**
 * DocumentReview - Review phase component for document workflow
 * Provides document validation, quality assessment, and review functionality
 */
const DocumentReview = () => {
  console.log('🚀 DocumentReview component rendering');

  const { documentId } = useParams();
  const navigate = useNavigate();
  const { isCollapsed, contentMargin } = useSidebar();
  const { user, profile } = useAuth();
  const [documentData, setDocumentData] = useState(null);
  const [generatedContent, setGeneratedContent] = useState(null);

  console.log('🔍 DocumentReview state:', {
    documentId,
    hasDocumentData: !!documentData,
    hasGeneratedContent: !!generatedContent,
    contentKeys: generatedContent ? Object.keys(generatedContent) : [],
    hasEditorHTML: !!(generatedContent?.editorHTML)
  });
  const [isValidating, setIsValidating] = useState(false);
  const [validationResults, setValidationResults] = useState(null);
  const [error, setError] = useState(null);

  // Review mode hook for managing review state
  const {
    reviewData,
    updateReviewData,
    updateReviewProgress,
    setValidationResults: setReviewValidationResults,
    completeReview,
    isReviewReadyForCompletion,
    reviewCompletionPercentage
  } = useReviewMode('Review');

  // Navigation handlers for DocumentInfoHeader
  const handlePublishClick = () => {
    console.log('Navigating to publish phase');
    navigate(`/document-editor/${documentId}/publish`);
  };

  const handleBackToEditClick = () => {
    console.log('Navigating back to edit phase');
    navigate(`/document-editor/${documentId}`);
  };



  // Load document data from database on component mount
  useEffect(() => {
    const loadDocumentData = async () => {
      try {
        console.log('🔄 Review phase: Loading latest document from database', { documentId });

        // Force fresh load from database to ensure latest content
        const result = await documentStorage.loadDocument(documentId, { forceFresh: true });

        if (result.success) {
          const data = result.data;

          console.log('✅ Review phase: Document loaded successfully', {
            documentId,
            source: result.source,
            hasGeneratedContent: !!data.generated_content,
            lastModified: data.updated_at,
            hasEditorHTML: !!(data.generated_content?.editorHTML)
          });

          // Convert database format to component format
          const documentData = {
            ...data.questionnaire_data,
            generatedContent: data.generated_content,
            lastModified: data.updated_at,
            createdAt: data.created_at,
            documentId: documentId,
            projectId: documentId
          };

          setDocumentData(documentData);
          setGeneratedContent(data.generated_content);

          console.log('📄 Review content loaded:', {
            hasContent: !!data.generated_content,
            hasEditorHTML: !!(data.generated_content?.editorHTML),
            wordCount: data.generated_content?.wordCount,
            chapterCount: data.generated_content?.chapters?.length
          });
        } else {
          console.error('❌ Failed to load document for review:', result.error);
          setError('Document not found');
        }
      } catch (error) {
        console.error('❌ Error loading document for review:', error);
        setError('Failed to load document');
      }
    };

    if (documentId) {
      loadDocumentData();

      // Update progress to Review phase when component loads
      handlePhaseTransition(documentId, 'Review', 'Edit Content')
        .then(() => {
          console.log('Progress updated to Review phase (75%)');
        })
        .catch(error => {
          console.warn('Failed to update progress to Review phase:', error);
        });
    }
  }, [documentId]);

  // Auto-validate document when content is loaded
  useEffect(() => {
    if (generatedContent && !validationResults && !isValidating) {
      handleValidateDocument();
    }
  }, [generatedContent, validationResults, isValidating]);

  // Handle document validation
  const handleValidateDocument = async () => {
    if (!generatedContent) {
      console.error('No content to validate');
      return;
    }

    setIsValidating(true);
    try {
      console.log('Starting document validation...');
      const results = await validateDocument(generatedContent);
      setValidationResults(results);
      setReviewValidationResults(results);
      console.log('Validation completed:', results);
    } catch (error) {
      console.error('Validation failed:', error);
      setError('Failed to validate document');
    } finally {
      setIsValidating(false);
    }
  };

  // Handle phase navigation
  const handlePhaseNavigation = (phase) => {
    console.log(`Navigating to phase: ${phase}`);

    switch (phase) {
      case 'Generate':
        // Navigate back to document creator
        navigate('/document-creator');
        break;
      case 'Edit Content':
        navigate(`/document-editor/${documentId}`);
        break;
      case 'Review':
        // Already on review page
        break;
      case 'Publish':
        // Update progress before navigating to publish phase
        handlePhaseTransition(documentId, 'Publish', 'Review')
          .then(() => {
            console.log('Progress updated for Publish phase from Review');
            navigate(`/document-editor/${documentId}/publish`);
          })
          .catch(error => {
            console.warn('Failed to update progress for Publish phase:', error);
            // Still navigate even if progress update fails
            navigate(`/document-editor/${documentId}/publish`);
          });
        break;
      default:
        console.warn(`Unknown phase: ${phase}`);
    }
  };

  // Handle review completion
  const handleCompleteReview = () => {
    completeReview();
    updateReviewProgress('reviewCompleted', true);
    console.log('Review marked as complete');
  };

  // Handle navigation to publish
  const handleNavigateToPublish = () => {
    // Save current state before navigating
    if (documentData && generatedContent) {
      const documentToSave = {
        ...documentData,
        generatedContent: generatedContent,
        reviewData: reviewData,
        lastModified: new Date().toISOString()
      };
      localStorage.setItem(`document-${documentId}`, JSON.stringify(documentToSave));
    }
    
    navigate(`/document-editor/${documentId}/publish`);
  };

  // Get document title for display
  const getDocumentTitle = () => {
    return documentData?.titleSelection?.selectedTitle || 
           documentData?.title || 
           'Untitled Document';
  };

  if (error) {
    return (
      <div className="min-h-screen bg-background">
        <QuickActionSidebar />
        <main className={`${contentMargin} ml-0`}>
          <DocumentWorkflowHeader
            currentPhase="Review"
            onPhaseClick={handlePhaseNavigation}
          />
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Icon name="AlertCircle" size={48} className="text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Document</h2>
              <p className="text-gray-600 mb-4">{error}</p>
              <button
                onClick={() => navigate('/dashboard')}
                className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
              >
                Return to Dashboard
              </button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (!documentData || !generatedContent) {
    return (
      <div className="min-h-screen bg-background">
        <QuickActionSidebar />
        <main className={`${contentMargin} ml-0`}>
          <DocumentWorkflowHeader
            currentPhase="Review"
            onPhaseClick={handlePhaseNavigation}
          />
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600">Loading document...</p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <QuickActionSidebar />
      
      <main className={`${contentMargin} ml-0 pt-16`}>
        {/* Document Workflow Header */}
        <DocumentWorkflowHeader
          currentPhase="Review"
          onPhaseClick={handlePhaseNavigation}
        />

        {/* Document Info Header */}
        <DocumentInfoHeader
          documentTitle={generatedContent?.title}
          documentData={documentData}
          generatedContent={generatedContent}
          currentPhase="Review"
          primaryButtonText="Publish"
          primaryButtonIcon="Send"
          primaryButtonAction={handlePublishClick}
          secondaryButtonText="Back to Edit"
          secondaryButtonIcon="ArrowLeft"
          secondaryButtonAction={handleBackToEditClick}
          showSaveStatus={false}
        />

        {/* Review Content */}
        <div className="bg-gray-50 overflow-hidden" style={{ height: 'calc(100vh - 10rem)' }}>
          <div className="h-full flex">
            {/* Main Document View */}
            <div className="flex-1 overflow-hidden">
              <DocumentCanvasMinimal
                content={generatedContent}
                isLoading={false}
                isReadOnly={true}
              />
            </div>


          </div>
        </div>
      </main>
    </div>
  );
};

export default DocumentReview;
