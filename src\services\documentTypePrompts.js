/**
 * Document Type-Specific AI Prompt Factory
 * Generates specialized AI prompts based on document type and configuration
 * Integrates with existing documentTypeConfigs to create type-appropriate content
 */

import { documentTypeConfigs } from '../pages/document-creator/utils/questionnaireDataStructure.js';

/**
 * Main factory function to create document type-specific prompts
 * @param {Object} documentData - Complete document configuration from questionnaire
 * @param {string} promptType - Type of prompt to generate ('outline', 'title', 'content', 'introduction', 'chapter')
 * @param {Object} chapterData - Additional data for chapter-specific prompts (optional)
 * @returns {string} Specialized AI prompt for the document type
 */
export const createDocumentTypePrompt = (documentData, promptType = 'outline', chapterData = null) => {
  const documentType = documentData.documentPurpose?.primaryType || 'ebook';
  const subType = documentData.documentPurpose?.subType;
  const config = documentTypeConfigs[documentType];
  
  if (!config) {
    console.warn(`No configuration found for document type: ${documentType}, using generic prompt`);
    return createGenericPrompt(documentData, promptType);
  }
  
  // Merge configuration with user data to get complete requirements
  const requirements = mergeDocumentRequirements(documentData, config);
  
  // Route to appropriate prompt creator based on document type
  switch (documentType) {
    case 'academic':
      return createAcademicPrompt(documentData, requirements, subType, promptType, chapterData);
    case 'business':
      return createBusinessPrompt(documentData, requirements, subType, promptType, chapterData);
    case 'guide':
      return createGuidePrompt(documentData, requirements, subType, promptType, chapterData);
    case 'creative':
      return createCreativePrompt(documentData, requirements, subType, promptType, chapterData);

    case 'ebook':
      return createEbookPrompt(documentData, requirements, subType, promptType, chapterData);
    default:
      return createGenericPrompt(documentData, promptType, chapterData);
  }
};

/**
 * Merge document type configuration with user-provided data
 * @param {Object} documentData - User questionnaire data
 * @param {Object} config - Document type configuration
 * @returns {Object} Merged requirements object
 */
const mergeDocumentRequirements = (documentData, config) => {
  return {
    // Start with document type defaults
    ...config.defaultRequirements,
    
    // Override with user-specific choices
    citationStyle: documentData.additionalRequirements?.citationStyle || 
                   config.defaultRequirements?.citationStyle || 'none',
    researchRequired: documentData.contentDetails?.researchRequired ?? 
                      config.defaultRequirements?.researchRequired ?? false,
    formalityLevel: documentData.toneAndVoice?.formalityLevel || 
                    config.defaultRequirements?.formalityLevel || 'formal',
    writingStyle: documentData.toneAndVoice?.writingStyle || 
                  config.defaultRequirements?.writingStyle || 'professional',
    includeExecutiveSummary: config.defaultRequirements?.includeExecutiveSummary ?? false,
    visualElements: config.defaultRequirements?.visualElements || {},
    organizationStyle: config.defaultRequirements?.organizationStyle || 'logical',
    voicePreference: config.defaultRequirements?.voicePreference || 'third-person'
  };
};

/**
 * Extract common context data from document configuration
 * @param {Object} documentData - Document configuration
 * @returns {Object} Base context for prompt generation
 */
const extractBaseContext = (documentData) => {
  return {
    topic: documentData.topicAndNiche?.mainTopic || 'General Topic',
    audience: documentData.audienceAnalysis?.primaryAudience || 'General Audience',
    title: documentData.titleSelection?.selectedTitle || `Guide to ${documentData.topicAndNiche?.mainTopic || 'Topic'}`,
    tone: documentData.toneAndVoice?.toneOfVoice || 'informative',
    subNiches: documentData.topicAndNiche?.subNiches || [],
    language: documentData.topicAndNiche?.language || 'english'
  };
};

/**
 * Create academic document prompts (research papers, theses, essays)
 * @param {Object} documentData - Document configuration
 * @param {Object} requirements - Merged requirements
 * @param {string} subType - Academic sub-type (research-paper, thesis, essay, etc.)
 * @param {string} promptType - Type of prompt to generate
 * @returns {string} Academic-specific AI prompt
 */
const createAcademicPrompt = (documentData, requirements, subType, promptType, chapterData) => {
  const baseContext = extractBaseContext(documentData);
  const academicSections = getAcademicSections(subType);
  
  if (promptType === 'outline') {
    return `You are an expert academic researcher and scholarly writer specializing in ${baseContext.topic}. Create a comprehensive ${subType || 'academic paper'} outline for peer review and academic publication.

DOCUMENT SPECIFICATIONS:
- Type: Academic ${subType || 'Paper'}
- Title: ${baseContext.title}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Academic Level: Graduate/Research level

ACADEMIC REQUIREMENTS:
- Citation Style: ${requirements.citationStyle?.toUpperCase() || 'APA'}
- Research Level: ${requirements.researchRequired ? 'Extensive peer-reviewed sources required' : 'Moderate research with credible sources'}
- Formality Level: ${requirements.formalityLevel || 'Very formal'}
- Writing Style: ${requirements.writingStyle || 'Academic'}
- Voice: ${requirements.voicePreference || 'Third-person academic'}

REQUIRED ACADEMIC SECTIONS:
${academicSections.map(section => `- ${section.name}: ${section.description} (${section.wordCount})`).join('\n')}

ACADEMIC STANDARDS:
- Follow ${requirements.citationStyle?.toUpperCase() || 'APA'} citation format throughout
- Include comprehensive literature review with peer-reviewed sources
- Maintain formal academic tone (avoid contractions, colloquialisms)
- Use third-person perspective and passive voice where appropriate
- Include methodology section with clear research design
- Provide statistical analysis and data interpretation where applicable
- Include limitations and future research directions
- Structure for peer review and academic publication standards

CONTENT REQUIREMENTS:
- Each section must be substantial and well-researched
- Include proper academic transitions between sections
- Maintain scholarly objectivity throughout
- Include relevant theoretical frameworks
- Provide evidence-based conclusions and recommendations
- Ensure logical flow from introduction to conclusion

Return ONLY a valid JSON object with this structure:
{
  "title": "Academic paper title",
  "abstract": "Brief abstract summarizing research (150-250 words)",
  "chapters": [
    {
      "number": 1,
      "title": "Section title",
      "sections": ["Subsection 1", "Subsection 2", "Subsection 3"],
      "wordCount": "estimated word count",
      "requirements": ["specific academic requirements for this section"]
    }
  ],
  "requiredElements": ["bibliography", "appendices if needed"],
  "citationStyle": "${requirements.citationStyle || 'APA'}",
  "academicLevel": "graduate"
}

JSON Response:`;
  }

  if (promptType === 'chapter' && chapterData) {
    return `You are an expert academic researcher and scholarly writer. Generate comprehensive content for this academic section:

Chapter ${chapterData.number}: ${chapterData.title}

ACADEMIC CONTEXT:
- Document Type: Academic ${subType || 'Paper'}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Citation Style: ${requirements.citationStyle?.toUpperCase() || 'APA'}
- Academic Level: Graduate/Research level

CHAPTER SECTIONS TO COVER:
${chapterData.sections?.map(section => `- ${section}`).join('\n') || '- Main content for this chapter'}

ACADEMIC WRITING REQUIREMENTS:
- Use formal academic language and third-person perspective
- Include relevant citations and references where appropriate
- Maintain scholarly objectivity and evidence-based arguments
- Follow ${requirements.citationStyle?.toUpperCase() || 'APA'} citation format
- Include theoretical framework and literature connections
- Provide critical analysis and interpretation
- Use appropriate academic terminology for the field

CONTENT STRUCTURE:
- Begin with clear section introduction
- Develop arguments with supporting evidence
- Include relevant research findings and data
- Provide critical analysis and interpretation
- Conclude with implications and connections to broader research
- Use proper academic transitions between subsections

LENGTH: 800-1200 words
TONE: Formal academic, objective, evidence-based

Generate the complete academic chapter content in markdown format:`;
  }

  // Handle other prompt types for academic documents
  return createAcademicContentPrompt(documentData, requirements, subType, promptType, chapterData);
};

/**
 * Get academic section structure based on sub-type
 * @param {string} subType - Academic document sub-type
 * @returns {Array} Array of section objects with requirements
 */
const getAcademicSections = (subType) => {
  const sections = {
    'research-paper': [
      { name: 'Abstract', description: 'Concise summary of research methodology and findings', wordCount: '150-250 words' },
      { name: 'Introduction', description: 'Problem statement, research questions, and significance', wordCount: '500-800 words' },
      { name: 'Literature Review', description: 'Comprehensive review of existing research and theoretical framework', wordCount: '1000-2000 words' },
      { name: 'Methodology', description: 'Research design, data collection, and analysis methods', wordCount: '800-1200 words' },
      { name: 'Results', description: 'Findings and data analysis with statistical interpretation', wordCount: '1000-1500 words' },
      { name: 'Discussion', description: 'Interpretation of results, implications, and limitations', wordCount: '1200-1800 words' },
      { name: 'Conclusion', description: 'Summary of contributions and future research directions', wordCount: '400-600 words' },
      { name: 'References', description: 'Properly formatted citations in required style', wordCount: 'varies' }
    ],
    'thesis': [
      { name: 'Abstract', description: 'Extended summary of thesis contributions and methodology', wordCount: '300-500 words' },
      { name: 'Introduction', description: 'Comprehensive problem introduction and research scope', wordCount: '2000-3000 words' },
      { name: 'Literature Review', description: 'Extensive review of field with critical analysis', wordCount: '5000-8000 words' },
      { name: 'Theoretical Framework', description: 'Theoretical foundation and conceptual model', wordCount: '3000-5000 words' },
      { name: 'Methodology', description: 'Detailed research methodology and justification', wordCount: '3000-4000 words' },
      { name: 'Analysis', description: 'Comprehensive data analysis and interpretation', wordCount: '8000-12000 words' },
      { name: 'Findings', description: 'Research findings and their significance', wordCount: '4000-6000 words' },
      { name: 'Conclusion', description: 'Conclusions, contributions, and future work', wordCount: '2000-3000 words' },
      { name: 'Bibliography', description: 'Comprehensive reference list', wordCount: 'varies' }
    ],
    'essay': [
      { name: 'Introduction', description: 'Thesis statement and essay overview', wordCount: '200-300 words' },
      { name: 'Body Paragraphs', description: 'Arguments supported by evidence and analysis', wordCount: '1500-2500 words' },
      { name: 'Counter-arguments', description: 'Addressing opposing viewpoints', wordCount: '300-500 words' },
      { name: 'Conclusion', description: 'Summary and final reflections', wordCount: '200-300 words' },
      { name: 'Works Cited', description: 'Reference list in required format', wordCount: 'varies' }
    ],
    'case-study': [
      { name: 'Executive Summary', description: 'Brief overview of case and key findings', wordCount: '300-500 words' },
      { name: 'Background', description: 'Context and background information', wordCount: '800-1200 words' },
      { name: 'Problem Statement', description: 'Clear definition of the problem or challenge', wordCount: '400-600 words' },
      { name: 'Analysis', description: 'Detailed analysis using relevant frameworks', wordCount: '2000-3000 words' },
      { name: 'Solutions', description: 'Proposed solutions and recommendations', wordCount: '1000-1500 words' },
      { name: 'Implementation', description: 'Implementation plan and considerations', wordCount: '800-1200 words' },
      { name: 'Conclusion', description: 'Lessons learned and implications', wordCount: '400-600 words' },
      { name: 'References', description: 'Academic sources and citations', wordCount: 'varies' }
    ]
  };
  
  return sections[subType] || sections['research-paper'];
};

/**
 * Create academic content prompts for specific sections
 * @param {Object} documentData - Document configuration
 * @param {Object} requirements - Academic requirements
 * @param {string} subType - Academic sub-type
 * @param {string} promptType - Specific prompt type
 * @returns {string} Academic content prompt
 */
const createAcademicContentPrompt = (documentData, requirements, subType, promptType, chapterData) => {
  const baseContext = extractBaseContext(documentData);
  
  if (promptType === 'title') {
    return `You are an expert academic researcher. Generate 8 scholarly titles for a ${subType || 'research paper'}:

Topic: ${baseContext.topic}
Academic Field: Determine appropriate field based on topic
Target Audience: ${baseContext.audience}
Citation Style: ${requirements.citationStyle}

ACADEMIC TITLE REQUIREMENTS:
- Follow academic title conventions (clear, specific, informative)
- Include key variables or concepts being studied
- Avoid sensational or clickbait language
- Use appropriate academic terminology
- Length: 10-15 words optimal for academic journals
- Include subtitle if it adds clarity

TITLE STYLES TO INCLUDE:
- Descriptive: Clear description of study focus
- Question-based: Research question format
- Declarative: Statement of findings or position
- Comparative: Comparing two or more elements

Return ONLY a valid JSON array with objects containing:
- id: unique identifier
- text: the academic title
- style: one of "descriptive", "question-based", "declarative", "comparative"
- field: suggested academic field/discipline

JSON Response:`;
  }
  
  if (promptType === 'introduction') {
    return `You are an expert academic researcher and scholarly writer. Write a compelling introduction for your ${subType || 'academic paper'}:

Title: ${baseContext.title}
Topic: ${baseContext.topic}
Academic Field: Determine appropriate field based on topic
Target Audience: ${baseContext.audience}
Citation Style: ${requirements.citationStyle?.toUpperCase() || 'APA'}

ACADEMIC INTRODUCTION REQUIREMENTS:
- Hook readers with compelling research question or problem statement
- Establish the significance and relevance of your research
- Provide necessary background context and literature foundation
- Clearly state your thesis, research questions, or hypotheses
- Outline the structure and scope of your paper
- Use formal academic language and third-person perspective
- Include relevant citations to establish credibility

INTRODUCTION STRUCTURE:
- Opening hook with research significance
- Background context and problem identification
- Literature gap or research opportunity
- Clear thesis statement or research questions
- Methodology overview (brief)
- Paper structure roadmap

ACADEMIC STANDARDS:
- Follow ${requirements.citationStyle?.toUpperCase() || 'APA'} citation format
- Use formal, scholarly language throughout
- Maintain objective, evidence-based tone
- Include proper academic transitions
- Establish theoretical framework foundation

Length: 500-800 words
Tone: Formal academic, objective, scholarly

Generate the complete academic introduction in markdown format:`;
  }

  if (promptType === 'conclusion') {
    return `You are an expert academic researcher and scholarly writer. Write a comprehensive conclusion for your ${subType || 'academic paper'}:

Title: ${baseContext.title}
Topic: ${baseContext.topic}
Academic Field: Determine appropriate field based on topic
Target Audience: ${baseContext.audience}
Citation Style: ${requirements.citationStyle?.toUpperCase() || 'APA'}

ACADEMIC CONCLUSION REQUIREMENTS:
- Summarize key research findings and their significance
- Restate thesis and demonstrate how it was supported
- Discuss implications for the field and broader applications
- Address limitations of the current research
- Suggest directions for future research
- Conclude with strong statement of contribution to knowledge
- Maintain scholarly objectivity throughout

CONCLUSION STRUCTURE:
- Restatement of research purpose and thesis
- Summary of key findings and evidence
- Discussion of implications and significance
- Acknowledgment of research limitations
- Suggestions for future research directions
- Final statement of contribution to field

ACADEMIC STANDARDS:
- Use formal, scholarly language and third-person perspective
- Follow ${requirements.citationStyle?.toUpperCase() || 'APA'} citation format
- Maintain objective, evidence-based tone
- Include relevant citations where appropriate
- Demonstrate critical thinking and analysis

Length: 400-600 words
Tone: Formal academic, objective, conclusive

Generate the complete academic conclusion in markdown format:`;
  }

  // Add more academic prompt types as needed
  return `Academic ${promptType} prompt for ${subType} - to be implemented`;
};

/**
 * Placeholder for generic prompt when no specific type is found
 * @param {Object} documentData - Document configuration
 * @param {string} promptType - Type of prompt
 * @returns {string} Generic prompt
 */
const createGenericPrompt = (documentData, promptType, chapterData) => {
  const baseContext = extractBaseContext(documentData);
  
  return `You are an expert content strategist and writer. Create a comprehensive ${promptType} for the following specifications:

Topic: ${baseContext.topic}
Target Audience: ${baseContext.audience}
Tone: ${baseContext.tone}

Requirements:
- Create well-structured content appropriate for the audience
- Maintain consistent tone throughout
- Ensure comprehensive coverage of the topic
- Make it practical and valuable

Return appropriate JSON structure for ${promptType}.`;
};

/**
 * Create business document prompts (proposals, market analysis, business plans)
 * @param {Object} documentData - Document configuration
 * @param {Object} requirements - Business requirements
 * @param {string} subType - Business sub-type (market-analysis, business-plan, etc.)
 * @param {string} promptType - Type of prompt to generate
 * @returns {string} Business-specific AI prompt
 */
const createBusinessPrompt = (documentData, requirements, subType, promptType, chapterData) => {
  const baseContext = extractBaseContext(documentData);
  const businessSections = getBusinessSections(subType);

  if (promptType === 'outline') {
    return `You are an expert business analyst and management consultant with extensive experience in ${baseContext.topic}. Create a comprehensive ${subType || 'business report'} for C-level executives and stakeholders.

DOCUMENT SPECIFICATIONS:
- Type: Business ${subType || 'Report'}
- Title: ${baseContext.title}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Business Context: Strategic decision-making and ROI analysis

BUSINESS REQUIREMENTS:
- Executive Summary: ${requirements.includeExecutiveSummary ? 'MANDATORY - Maximum 2 pages with key findings and ROI' : 'Optional'}
- Visual Elements: ${requirements.visualElements?.includeCharts ? 'Include charts, graphs, and data visualizations' : 'Text-focused with minimal visuals'}
- Organization Style: ${requirements.organizationStyle || 'Logical progression with clear recommendations'}
- Professional Tone: Confident, decisive, action-oriented language
- ROI Focus: Emphasize return on investment and business value

REQUIRED BUSINESS SECTIONS:
${businessSections.map(section => `- ${section.name}: ${section.description} (${section.wordCount})`).join('\n')}

BUSINESS STANDARDS:
- Lead with executive summary highlighting key recommendations and ROI
- Include specific KPIs, metrics, and measurable outcomes
- Focus on actionable recommendations with clear next steps
- Use confident, decisive language appropriate for executives
- Structure for board presentation and strategic decision-making
- Include competitive analysis and market positioning
- Provide implementation timeline with resource requirements
- Address risk assessment and mitigation strategies

CONTENT REQUIREMENTS:
- Each section must drive toward business value and ROI
- Include data-driven insights and market research
- Provide specific recommendations with cost-benefit analysis
- Use professional business terminology and frameworks
- Include implementation roadmap with clear milestones
- Address stakeholder concerns and decision criteria

FORMATTING REQUIREMENTS:
- Use bullet points and numbered lists for clarity
- Include executive-level summaries for each major section
- Provide clear headings and subheadings for easy navigation
- Include space for charts, graphs, and visual data representation

Return ONLY a valid JSON object with this structure:
{
  "title": "Business document title",
  "executiveSummary": "Brief executive summary with key findings and ROI (200-400 words)",
  "chapters": [
    {
      "number": 1,
      "title": "Section title",
      "sections": ["Subsection 1", "Subsection 2", "Subsection 3"],
      "wordCount": "estimated word count",
      "businessFocus": "specific business value or outcome",
      "keyMetrics": ["relevant KPIs or metrics to include"]
    }
  ],
  "requiredElements": ["executive summary", "recommendations", "implementation plan"],
  "businessType": "${subType || 'report'}",
  "targetAudience": "C-level executives"
}

JSON Response:`;
  }

  if (promptType === 'title') {
    return `You are an expert business strategist and executive communications specialist. Generate 8 compelling business document titles for a ${subType || 'business report'}:

Topic: ${baseContext.topic}
Business Context: ${getBusinessContext(baseContext.topic)}
Target Audience: ${baseContext.audience}
Document Purpose: Strategic business analysis and decision-making

BUSINESS TITLE REQUIREMENTS:
- Use professional business language that commands executive attention
- Include power words that convey value and urgency (Strategic, Comprehensive, Executive, Analysis)
- Focus on business outcomes and ROI implications
- Length: 8-12 words optimal for executive presentations
- Avoid jargon - use clear, decisive language
- Include action-oriented language when appropriate

TITLE STYLES TO INCLUDE:
- Strategic: Focus on strategic implications and competitive advantage
- Analytical: Emphasize data-driven insights and market analysis
- Executive: Designed for C-level presentation and decision-making
- Action-Oriented: Include implementation focus and next steps

BUSINESS POWER WORDS TO CONSIDER:
- Strategic, Executive, Comprehensive, Analysis, Insights
- ROI, Growth, Optimization, Transformation, Innovation
- Market, Competitive, Performance, Success, Results

Return ONLY a valid JSON array with objects containing:
- id: unique identifier
- text: the business title
- style: one of "strategic", "analytical", "executive", "action-oriented"
- businessFocus: primary business value proposition

JSON Response:`;
  }

  if (promptType === 'chapter' && chapterData) {
    return `You are an expert business analyst and management consultant. Generate comprehensive content for this business section:

Chapter ${chapterData.number}: ${chapterData.title}

BUSINESS CONTEXT:
- Document Type: Business ${subType || 'Report'}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Business Focus: ${getBusinessContext(baseContext.topic)}
- Objective: Strategic decision-making and ROI analysis

CHAPTER SECTIONS TO COVER:
${chapterData.sections?.map(section => `- ${section}`).join('\n') || '- Main content for this chapter'}

BUSINESS WRITING REQUIREMENTS:
- Use professional business language and confident tone
- Focus on actionable insights and recommendations
- Include relevant data, metrics, and KPIs where appropriate
- Provide specific implementation strategies and timelines
- Address ROI implications and business value
- Use executive-level language appropriate for C-suite presentation
- Include competitive analysis and market positioning where relevant

CONTENT STRUCTURE:
- Begin with clear business case and value proposition
- Develop arguments with supporting data and market research
- Include specific recommendations with cost-benefit analysis
- Provide implementation roadmap with clear milestones
- Address potential risks and mitigation strategies
- Conclude with measurable outcomes and success metrics

LENGTH: 800-1200 words
TONE: Professional, confident, results-oriented, executive-level

Generate the complete business chapter content in markdown format:`;
  }

  // Handle other prompt types for business documents
  return createBusinessContentPrompt(documentData, requirements, subType, promptType, chapterData);
};

/**
 * Get business section structure based on sub-type
 * @param {string} subType - Business document sub-type
 * @returns {Array} Array of section objects with business requirements
 */
const getBusinessSections = (subType) => {
  const sections = {
    'market-analysis': [
      { name: 'Executive Summary', description: 'Key findings, market size, and strategic recommendations', wordCount: '500-800 words' },
      { name: 'Market Overview', description: 'Market size, growth trends, and key drivers', wordCount: '1000-1500 words' },
      { name: 'Competitive Landscape', description: 'Key players, market positioning, and competitive advantages', wordCount: '1500-2000 words' },
      { name: 'Market Segmentation', description: 'Target segments, customer profiles, and market opportunities', wordCount: '1000-1500 words' },
      { name: 'Trends & Opportunities', description: 'Emerging trends, market gaps, and growth opportunities', wordCount: '1000-1500 words' },
      { name: 'Strategic Recommendations', description: 'Actionable recommendations with ROI projections', wordCount: '800-1200 words' },
      { name: 'Implementation Roadmap', description: 'Timeline, milestones, and resource requirements', wordCount: '600-1000 words' },
      { name: 'Risk Assessment', description: 'Potential challenges and mitigation strategies', wordCount: '400-600 words' },
      { name: 'Appendices', description: 'Supporting data, charts, and detailed analysis', wordCount: 'varies' }
    ],
    'business-plan': [
      { name: 'Executive Summary', description: 'Business concept, financial highlights, and funding requirements', wordCount: '800-1200 words' },
      { name: 'Company Description', description: 'Mission, vision, values, and business model', wordCount: '600-1000 words' },
      { name: 'Market Analysis', description: 'Industry overview, target market, and competitive analysis', wordCount: '1500-2500 words' },
      { name: 'Organization & Management', description: 'Organizational structure and management team', wordCount: '800-1200 words' },
      { name: 'Products & Services', description: 'Detailed description of offerings and value proposition', wordCount: '1000-1500 words' },
      { name: 'Marketing & Sales Strategy', description: 'Customer acquisition and revenue generation strategy', wordCount: '1200-1800 words' },
      { name: 'Financial Projections', description: 'Revenue forecasts, expense projections, and profitability analysis', wordCount: '1000-1500 words' },
      { name: 'Funding Requirements', description: 'Capital needs, use of funds, and exit strategy', wordCount: '600-1000 words' },
      { name: 'Risk Analysis', description: 'Business risks and contingency planning', wordCount: '400-800 words' }
    ],
    'proposal': [
      { name: 'Executive Summary', description: 'Project overview, benefits, and recommended solution', wordCount: '400-600 words' },
      { name: 'Problem Statement', description: 'Clear definition of business challenge or opportunity', wordCount: '600-1000 words' },
      { name: 'Proposed Solution', description: 'Detailed solution approach and methodology', wordCount: '1200-1800 words' },
      { name: 'Implementation Plan', description: 'Project timeline, phases, and deliverables', wordCount: '1000-1500 words' },
      { name: 'Team & Qualifications', description: 'Project team expertise and relevant experience', wordCount: '800-1200 words' },
      { name: 'Budget & Pricing', description: 'Detailed cost breakdown and pricing structure', wordCount: '600-1000 words' },
      { name: 'Expected Outcomes', description: 'Measurable benefits and ROI projections', wordCount: '600-1000 words' },
      { name: 'Terms & Conditions', description: 'Project terms, assumptions, and next steps', wordCount: '400-600 words' }
    ],
    'feasibility-study': [
      { name: 'Executive Summary', description: 'Study purpose, key findings, and recommendations', wordCount: '600-1000 words' },
      { name: 'Project Description', description: 'Detailed project scope and objectives', wordCount: '800-1200 words' },
      { name: 'Market Feasibility', description: 'Market demand, competition, and customer analysis', wordCount: '1500-2000 words' },
      { name: 'Technical Feasibility', description: 'Technical requirements, resources, and capabilities', wordCount: '1200-1800 words' },
      { name: 'Financial Feasibility', description: 'Cost analysis, revenue projections, and ROI assessment', wordCount: '1500-2000 words' },
      { name: 'Risk Assessment', description: 'Potential risks, challenges, and mitigation strategies', wordCount: '1000-1500 words' },
      { name: 'Recommendations', description: 'Go/no-go decision with supporting rationale', wordCount: '600-1000 words' },
      { name: 'Implementation Considerations', description: 'Next steps and implementation requirements', wordCount: '400-800 words' }
    ]
  };

  return sections[subType] || sections['market-analysis'];
};

/**
 * Get business context based on topic
 * @param {string} topic - Business topic
 * @returns {string} Business context description
 */
const getBusinessContext = (topic) => {
  const contexts = {
    'marketing': 'Digital marketing strategy and customer acquisition',
    'finance': 'Financial analysis and investment decision-making',
    'operations': 'Operational efficiency and process optimization',
    'strategy': 'Strategic planning and competitive positioning',
    'technology': 'Technology implementation and digital transformation',
    'sales': 'Sales strategy and revenue optimization',
    'hr': 'Human resources and organizational development',
    'consulting': 'Management consulting and business advisory'
  };

  // Try to match topic with business context
  const lowerTopic = topic.toLowerCase();
  for (const [key, context] of Object.entries(contexts)) {
    if (lowerTopic.includes(key)) {
      return context;
    }
  }

  return 'Strategic business analysis and decision-making';
};

/**
 * Create business content prompts for specific sections
 * @param {Object} documentData - Document configuration
 * @param {Object} requirements - Business requirements
 * @param {string} subType - Business sub-type
 * @param {string} promptType - Specific prompt type
 * @returns {string} Business content prompt
 */
const createBusinessContentPrompt = (documentData, requirements, subType, promptType) => {
  const baseContext = extractBaseContext(documentData);

  if (promptType === 'introduction') {
    return `You are an expert business analyst. Write a compelling introduction for a ${subType || 'business report'}:

Title: ${baseContext.title}
Topic: ${baseContext.topic}
Business Context: ${getBusinessContext(baseContext.topic)}
Target Audience: ${baseContext.audience}

BUSINESS INTRODUCTION REQUIREMENTS:
- Hook executives with compelling business case
- Clearly state the business problem or opportunity
- Outline key benefits and ROI potential
- Set expectations for strategic recommendations
- Use confident, professional business language
- Include relevant market context and urgency

STRUCTURE:
- Opening hook with business impact
- Problem/opportunity statement
- Document purpose and scope
- Key benefits preview
- Document roadmap

Length: 400-600 words
Tone: Professional, confident, results-oriented

Generate the business introduction in markdown format:`;
  }

  if (promptType === 'chapter' && chapterData) {
    return `You are an expert business analyst and management consultant. Generate comprehensive content for this business section:

Chapter ${chapterData.number}: ${chapterData.title}

BUSINESS CONTEXT:
- Document Type: Business ${subType || 'Report'}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Business Focus: ${getBusinessContext(baseContext.topic)}
- Objective: Strategic decision-making and ROI analysis

CHAPTER SECTIONS TO COVER:
${chapterData.sections?.map(section => `- ${section}`).join('\n') || '- Main content for this chapter'}

BUSINESS WRITING REQUIREMENTS:
- Use professional business language and confident tone
- Focus on actionable insights and recommendations
- Include relevant data, metrics, and KPIs where appropriate
- Emphasize ROI and business value propositions
- Provide clear implementation guidance
- Use executive-level communication style
- Include competitive analysis and market insights

CONTENT STRUCTURE:
- Begin with clear business case or problem statement
- Develop arguments with supporting data and market research
- Include specific recommendations with cost-benefit analysis
- Provide implementation roadmap and resource requirements
- Conclude with measurable outcomes and success metrics
- Use professional transitions and business frameworks

LENGTH: 800-1200 words
TONE: Professional, confident, results-oriented, executive-level

Generate the complete business chapter content in markdown format:`;
  }

  if (promptType === 'conclusion') {
    return `You are an expert business analyst and management consultant. Write a powerful conclusion for your ${subType || 'business report'}:

Title: ${baseContext.title}
Topic: ${baseContext.topic}
Business Context: ${getBusinessContext(baseContext.topic)}
Target Audience: ${baseContext.audience}
Document Purpose: Strategic business analysis and decision-making

BUSINESS CONCLUSION REQUIREMENTS:
- Summarize key findings and strategic insights
- Present clear, actionable recommendations with ROI implications
- Provide implementation roadmap with timelines and resources
- Address potential risks and mitigation strategies
- Include measurable success metrics and KPIs
- End with compelling call-to-action for stakeholders
- Use confident, decisive executive language

CONCLUSION STRUCTURE:
- Executive summary of key findings
- Strategic recommendations with business impact
- Implementation plan with resource requirements
- Risk assessment and mitigation strategies
- Success metrics and measurement framework
- Next steps and call-to-action

BUSINESS STANDARDS:
- Focus on ROI and business value propositions
- Use professional, confident language appropriate for executives
- Include specific, measurable outcomes
- Provide clear implementation guidance
- Address stakeholder concerns and decision criteria
- Emphasize competitive advantage and market positioning

Length: 400-600 words
Tone: Professional, confident, results-oriented, decisive

Generate the complete business conclusion in markdown format:`;
  }

  // Add more business prompt types as needed
  return `Business ${promptType} prompt for ${subType} - to be implemented`;
};

/**
 * Create guide document prompts (how-to guides, tutorials, instructional content)
 * @param {Object} documentData - Document configuration
 * @param {Object} requirements - Guide requirements
 * @param {string} subType - Guide sub-type (how-to-guide, tutorial, user-guide, etc.)
 * @param {string} promptType - Type of prompt to generate
 * @returns {string} Guide-specific AI prompt
 */
const createGuidePrompt = (documentData, requirements, subType, promptType, chapterData) => {
  const baseContext = extractBaseContext(documentData);
  const guideSections = getGuideSections(subType);

  if (promptType === 'outline') {
    return `You are an expert instructional designer and technical writer specializing in ${baseContext.topic}. Create a comprehensive step-by-step ${subType || 'how-to guide'} that enables readers to successfully achieve their goals.

DOCUMENT SPECIFICATIONS:
- Type: Instructional ${subType || 'Guide'}
- Title: ${baseContext.title}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Skill Level: Beginner to intermediate (adjust complexity as needed)

INSTRUCTIONAL REQUIREMENTS:
- Sequential Structure: Clear step-by-step progression with numbered instructions
- Practical Focus: Real-world examples and hands-on activities
- Troubleshooting: Common problems and solutions for each major step
- Prerequisites: Clear requirements before starting
- Visual Aids: Placeholders for screenshots, diagrams, and illustrations
- Progress Checkpoints: Validation points to ensure readers are on track
- Tools & Resources: Specific recommendations with explanations

REQUIRED GUIDE SECTIONS:
${guideSections.map(section => `- ${section.name}: ${section.description} (${section.wordCount})`).join('\n')}

INSTRUCTIONAL STANDARDS:
- Use clear, conversational language (2nd person "you")
- Include specific time estimates for each major step
- Provide alternative approaches for different skill levels or situations
- Include safety warnings or important notes where relevant
- Use encouraging, supportive tone throughout
- Break complex tasks into manageable sub-steps
- Include "what you'll learn" and "what you'll need" sections
- Provide troubleshooting for common mistakes

CONTENT REQUIREMENTS:
- Each step must be actionable and specific
- Include rationale for why steps are important
- Provide examples and real-world applications
- Use consistent formatting and terminology
- Include progress indicators and milestones
- Address different learning styles (visual, auditory, kinesthetic)

FORMATTING REQUIREMENTS:
- Use numbered lists for sequential steps
- Use bullet points for options or lists
- Include clear headings and subheadings
- Provide space for images, screenshots, and diagrams
- Use callout boxes for tips, warnings, and important notes

Return ONLY a valid JSON object with this structure:
{
  "title": "Step-by-step guide title",
  "overview": "What readers will accomplish and learn (100-200 words)",
  "prerequisites": ["Required knowledge", "Required tools", "Time needed"],
  "chapters": [
    {
      "number": 1,
      "title": "Step or chapter title",
      "sections": ["Sub-step 1", "Sub-step 2", "Sub-step 3"],
      "wordCount": "estimated word count",
      "timeEstimate": "estimated completion time",
      "difficulty": "beginner/intermediate/advanced",
      "keyOutcomes": ["what readers will achieve in this step"]
    }
  ],
  "requiredElements": ["prerequisites", "troubleshooting", "next steps"],
  "guideType": "${subType || 'how-to-guide'}",
  "skillLevel": "beginner-friendly"
}

JSON Response:`;
  }

  if (promptType === 'title') {
    return `You are an expert instructional designer and content strategist. Generate 8 compelling guide titles for a ${subType || 'how-to guide'}:

Topic: ${baseContext.topic}
Target Audience: ${baseContext.audience}
Guide Purpose: Step-by-step instruction and skill development
Learning Context: ${getGuideContext(baseContext.topic)}

GUIDE TITLE REQUIREMENTS:
- Use clear, action-oriented language that promises specific outcomes
- Include power words that convey learning and achievement (Complete, Master, Step-by-Step, Ultimate)
- Focus on the end result or transformation readers will achieve
- Length: 6-10 words optimal for instructional content
- Avoid overly technical jargon - use accessible language
- Include difficulty level indicators when helpful (Beginner's, Complete, Ultimate)

TITLE STYLES TO INCLUDE:
- How-To: Direct instruction format ("How to...")
- Complete Guide: Comprehensive coverage ("The Complete Guide to...")
- Step-by-Step: Process-focused ("Step-by-Step Guide to...")
- Mastery: Skill development focus ("Master [Topic] in [Timeframe]")

INSTRUCTIONAL POWER WORDS TO CONSIDER:
- Complete, Ultimate, Step-by-Step, Master, Learn
- Beginner's, Essential, Practical, Hands-On, Quick
- Guide, Tutorial, Handbook, Blueprint, Course

Return ONLY a valid JSON array with objects containing:
- id: unique identifier
- text: the guide title
- style: one of "how-to", "complete-guide", "step-by-step", "mastery"
- learningOutcome: what readers will achieve

JSON Response:`;
  }

  if (promptType === 'chapter' && chapterData) {
    return `You are an expert instructional designer and technical writer. Generate comprehensive content for this instructional section:

Chapter ${chapterData.number}: ${chapterData.title}

INSTRUCTIONAL CONTEXT:
- Document Type: ${subType || 'How-to Guide'}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Learning Context: ${getGuideContext(baseContext.topic)}
- Objective: Enable readers to successfully complete specific tasks

CHAPTER SECTIONS TO COVER:
${chapterData.sections?.map(section => `- ${section}`).join('\n') || '- Main instructional content for this chapter'}

INSTRUCTIONAL WRITING REQUIREMENTS:
- Use clear, step-by-step instructions with numbered or bulleted lists
- Include practical examples and real-world applications
- Provide troubleshooting tips and common mistake warnings
- Use simple, accessible language appropriate for the target audience
- Include visual cues and formatting for easy scanning
- Break complex processes into manageable chunks
- Provide checkpoints and progress indicators

CONTENT STRUCTURE:
- Begin with clear learning objectives and prerequisites
- Present information in logical, sequential order
- Include hands-on exercises and practice opportunities
- Provide troubleshooting sections for common issues
- Include tips, warnings, and best practices throughout
- Conclude with summary and next steps

LENGTH: 800-1200 words
TONE: Encouraging, clear, supportive, practical

Generate the complete instructional chapter content in markdown format:`;
  }

  // Handle other prompt types for guide documents
  return createGuideContentPrompt(documentData, requirements, subType, promptType, chapterData);
};

/**
 * Get guide section structure based on sub-type
 * @param {string} subType - Guide document sub-type
 * @returns {Array} Array of section objects with instructional requirements
 */
const getGuideSections = (subType) => {
  const sections = {
    'how-to-guide': [
      { name: 'Introduction & Overview', description: 'What you\'ll learn and why it matters', wordCount: '300-500 words' },
      { name: 'Prerequisites & Requirements', description: 'What you need before starting', wordCount: '200-400 words' },
      { name: 'Tools & Materials', description: 'Required tools, software, or materials with recommendations', wordCount: '300-600 words' },
      { name: 'Step 1: Getting Started', description: 'Initial setup and preparation', wordCount: '500-800 words' },
      { name: 'Step 2: Core Process', description: 'Main instructional content with detailed steps', wordCount: '1000-1500 words' },
      { name: 'Step 3: Advanced Techniques', description: 'More sophisticated approaches and optimizations', wordCount: '800-1200 words' },
      { name: 'Step 4: Finishing & Review', description: 'Final steps and quality checks', wordCount: '400-600 words' },
      { name: 'Troubleshooting Guide', description: 'Common problems and solutions', wordCount: '600-1000 words' },
      { name: 'Tips & Best Practices', description: 'Expert advice and optimization tips', wordCount: '400-800 words' },
      { name: 'Next Steps & Resources', description: 'What to do next and additional learning resources', wordCount: '300-500 words' }
    ],
    'tutorial': [
      { name: 'Tutorial Overview', description: 'Learning objectives and tutorial structure', wordCount: '200-400 words' },
      { name: 'Setup & Preparation', description: 'Environment setup and initial configuration', wordCount: '400-600 words' },
      { name: 'Lesson 1: Fundamentals', description: 'Basic concepts and first hands-on exercise', wordCount: '800-1200 words' },
      { name: 'Lesson 2: Building Skills', description: 'Intermediate concepts with practical application', wordCount: '1000-1500 words' },
      { name: 'Lesson 3: Advanced Application', description: 'Complex scenarios and real-world application', wordCount: '1200-1800 words' },
      { name: 'Practice Exercises', description: 'Hands-on exercises to reinforce learning', wordCount: '600-1000 words' },
      { name: 'Common Mistakes', description: 'Typical errors and how to avoid them', wordCount: '400-600 words' },
      { name: 'Assessment & Review', description: 'Self-assessment and knowledge check', wordCount: '300-500 words' },
      { name: 'Further Learning', description: 'Advanced topics and continued education paths', wordCount: '300-500 words' }
    ],
    'user-guide': [
      { name: 'Getting Started', description: 'Initial setup, installation, and first use', wordCount: '600-1000 words' },
      { name: 'Basic Operations', description: 'Core functionality and everyday tasks', wordCount: '1000-1500 words' },
      { name: 'Advanced Features', description: 'Sophisticated functionality and customization', wordCount: '1200-1800 words' },
      { name: 'Configuration & Settings', description: 'Customization options and preferences', wordCount: '800-1200 words' },
      { name: 'Maintenance & Updates', description: 'Keeping the system running smoothly', wordCount: '400-800 words' },
      { name: 'Troubleshooting', description: 'Problem diagnosis and resolution', wordCount: '800-1200 words' },
      { name: 'FAQ', description: 'Frequently asked questions and answers', wordCount: '600-1000 words' },
      { name: 'Technical Specifications', description: 'System requirements and technical details', wordCount: '300-600 words' },
      { name: 'Support & Resources', description: 'Getting help and additional resources', wordCount: '200-400 words' }
    ],
    'best-practices': [
      { name: 'Introduction to Best Practices', description: 'Why best practices matter and overview', wordCount: '400-600 words' },
      { name: 'Foundational Principles', description: 'Core principles and underlying concepts', wordCount: '800-1200 words' },
      { name: 'Planning & Preparation', description: 'How to plan and prepare effectively', wordCount: '1000-1500 words' },
      { name: 'Implementation Strategies', description: 'Proven approaches and methodologies', wordCount: '1200-1800 words' },
      { name: 'Quality Assurance', description: 'Ensuring quality and avoiding common pitfalls', wordCount: '800-1200 words' },
      { name: 'Optimization Techniques', description: 'Advanced optimization and efficiency improvements', wordCount: '1000-1500 words' },
      { name: 'Monitoring & Evaluation', description: 'Measuring success and continuous improvement', wordCount: '600-1000 words' },
      { name: 'Case Studies', description: 'Real-world examples and lessons learned', wordCount: '800-1200 words' },
      { name: 'Implementation Checklist', description: 'Step-by-step checklist for implementation', wordCount: '400-600 words' }
    ]
  };

  return sections[subType] || sections['how-to-guide'];
};

/**
 * Get guide context based on topic
 * @param {string} topic - Guide topic
 * @returns {string} Learning context description
 */
const getGuideContext = (topic) => {
  const contexts = {
    'technology': 'Technical skill development and software mastery',
    'business': 'Professional skill development and business process improvement',
    'creative': 'Creative skill development and artistic technique mastery',
    'health': 'Health and wellness improvement and lifestyle changes',
    'education': 'Learning strategies and educational skill development',
    'finance': 'Financial literacy and money management skills',
    'productivity': 'Productivity improvement and efficiency optimization',
    'communication': 'Communication skills and relationship building'
  };

  // Try to match topic with learning context
  const lowerTopic = topic.toLowerCase();
  for (const [key, context] of Object.entries(contexts)) {
    if (lowerTopic.includes(key)) {
      return context;
    }
  }

  return 'Practical skill development and hands-on learning';
};

/**
 * Create guide content prompts for specific sections
 * @param {Object} documentData - Document configuration
 * @param {Object} requirements - Guide requirements
 * @param {string} subType - Guide sub-type
 * @param {string} promptType - Specific prompt type
 * @returns {string} Guide content prompt
 */
const createGuideContentPrompt = (documentData, requirements, subType, promptType) => {
  const baseContext = extractBaseContext(documentData);

  if (promptType === 'introduction') {
    return `You are an expert instructional designer. Write an engaging introduction for a ${subType || 'how-to guide'}:

Title: ${baseContext.title}
Topic: ${baseContext.topic}
Learning Context: ${getGuideContext(baseContext.topic)}
Target Audience: ${baseContext.audience}

GUIDE INTRODUCTION REQUIREMENTS:
- Hook readers with the value and benefits they'll gain
- Clearly explain what they'll be able to do after completing the guide
- Set realistic expectations for time, difficulty, and prerequisites
- Use encouraging, supportive language that builds confidence
- Include a brief overview of the step-by-step process
- Address common concerns or hesitations

STRUCTURE:
- Opening hook with transformation promise
- Clear learning outcomes
- Time and difficulty expectations
- Prerequisites overview
- Process roadmap
- Encouragement and motivation

Length: 400-600 words
Tone: Encouraging, clear, supportive, practical

Generate the guide introduction in markdown format:`;
  }

  if (promptType === 'chapter' && chapterData) {
    return `You are an expert instructional designer and technical writer. Generate comprehensive content for this instructional section:

Chapter ${chapterData.number}: ${chapterData.title}

INSTRUCTIONAL CONTEXT:
- Document Type: ${subType || 'How-to Guide'}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Learning Context: ${getGuideContext(baseContext.topic)}
- Objective: Enable readers to successfully complete specific tasks

CHAPTER SECTIONS TO COVER:
${chapterData.sections?.map(section => `- ${section}`).join('\n') || '- Main instructional content for this chapter'}

INSTRUCTIONAL WRITING REQUIREMENTS:
- Use clear, step-by-step instructions with numbered or bulleted lists
- Include practical examples and real-world applications
- Provide troubleshooting tips and common mistake warnings
- Use encouraging, supportive language that builds confidence
- Include visual cues and formatting for easy scanning
- Break complex tasks into manageable sub-steps
- Add time estimates and difficulty indicators where helpful

CONTENT STRUCTURE:
- Begin with clear learning objectives for this chapter
- Provide step-by-step instructions with detailed explanations
- Include practical examples and hands-on exercises
- Add troubleshooting section for common issues
- Conclude with summary and next steps
- Use clear headings and formatting for easy navigation

LENGTH: 800-1200 words
TONE: Instructional, encouraging, clear, practical

Generate the complete instructional chapter content in markdown format:`;
  }

  if (promptType === 'conclusion') {
    return `You are an expert instructional designer and technical writer. Write an inspiring conclusion for your ${subType || 'how-to guide'}:

Title: ${baseContext.title}
Topic: ${baseContext.topic}
Learning Context: ${getGuideContext(baseContext.topic)}
Target Audience: ${baseContext.audience}
Guide Purpose: Step-by-step instruction and skill development

GUIDE CONCLUSION REQUIREMENTS:
- Congratulate readers on completing the learning journey
- Summarize key skills and knowledge gained
- Reinforce the value and benefits of what they've learned
- Provide next steps for continued learning and improvement
- Include troubleshooting resources and support information
- Encourage readers to apply their new skills immediately
- Use motivational, supportive language that builds confidence

CONCLUSION STRUCTURE:
- Congratulations and achievement recognition
- Summary of skills and knowledge acquired
- Reinforcement of benefits and value gained
- Next steps and advanced learning opportunities
- Additional resources and support information
- Encouragement for immediate application
- Final motivational message

INSTRUCTIONAL STANDARDS:
- Use encouraging, supportive language that builds confidence
- Include practical next steps and continued learning paths
- Provide additional resources for further development
- Address common post-completion concerns or questions
- Use second-person "you" to maintain personal connection
- Include specific examples of how to apply new skills

Length: 400-600 words
Tone: Encouraging, supportive, motivational, practical

Generate the complete guide conclusion in markdown format:`;
  }

  // Add more guide prompt types as needed
  return `Guide ${promptType} prompt for ${subType} - to be implemented`;
};

const createCreativePrompt = (documentData, requirements, subType, promptType, chapterData) => {
  const baseContext = extractBaseContext(documentData);

  if (promptType === 'outline') {
    return `You are an expert creative writer and storytelling coach specializing in ${subType || 'creative writing'}. Create a compelling ${subType} outline that will captivate readers and deliver a satisfying narrative experience.

CREATIVE SPECIFICATIONS:
- Genre: ${subType || 'Creative Writing'}
- Title: ${baseContext.title}
- Topic/Theme: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Narrative Style: ${getCreativeStyle(subType)}
- Voice: ${requirements.voicePreference || 'First-person narrative'}

STORYTELLING REQUIREMENTS:
- Character Development: ${getCharacterRequirements(subType)}
- Plot Structure: ${getPlotStructure(subType)}
- Pacing: Engaging rhythm with tension and release
- Theme: ${baseContext.topic} explored through narrative
- Tone: ${baseContext.tone || 'Engaging and immersive'}

CREATIVE ELEMENTS:
${getCreativeSections(subType).map(section => `- ${section.name}: ${section.description} (${section.wordCount})`).join('\n')}

NARRATIVE STANDARDS:
- Create compelling character arcs and development
- Build tension and conflict throughout the story
- Use vivid, sensory descriptions and imagery
- Include authentic dialogue and character voice
- Maintain consistent point of view and tense
- Develop themes through action and character growth
- Create satisfying resolution or meaningful conclusion

Return ONLY a valid JSON object with this structure:
{
  "title": "Compelling creative title",
  "genre": "${subType || 'creative writing'}",
  "premise": "Core story premise or concept (50-100 words)",
  "characters": [
    {
      "name": "Character name",
      "role": "protagonist/antagonist/supporting",
      "description": "Character description and arc",
      "motivation": "What drives this character"
    }
  ],
  "chapters": [
    {
      "number": 1,
      "title": "Chapter title",
      "purpose": "What this chapter accomplishes",
      "keyEvents": ["Major event 1", "Major event 2"],
      "wordCount": "estimated word count",
      "emotionalArc": "emotional journey in this chapter",
      "conflict": "primary conflict or tension"
    }
  ],
  "themes": ["primary theme", "secondary theme"],
  "targetLength": "${getCreativeLength(subType)}",
  "narrativeStyle": "${subType} conventions and structure"
}

JSON Response:`;
  }

  if (promptType === 'title') {
    return `You are an expert creative writer and publishing strategist. Generate 8 compelling creative titles for a ${subType || 'creative work'}:

Topic/Theme: ${baseContext.topic}
Target Audience: ${baseContext.audience}
Creative Purpose: ${getCreativePurpose(subType)}
Genre Context: ${getCreativeContext(subType)}

CREATIVE TITLE REQUIREMENTS:
- Create titles that evoke emotion and intrigue
- Use literary devices like metaphor, alliteration, or symbolism
- Capture the essence of your theme or story
- Appeal to your target audience's interests and emotions
- Length: 1-6 words optimal for creative works
- Consider genre conventions and reader expectations
- Avoid clichés while honoring genre traditions

TITLE STYLES TO INCLUDE:
- Evocative: Emotionally resonant and atmospheric
- Symbolic: Using metaphor or deeper meaning
- Character-Driven: Focusing on protagonist or relationships
- Thematic: Highlighting central themes or concepts

CREATIVE POWER WORDS TO CONSIDER:
- ${getCreativeKeywords(subType)}
- Emotion words: Heart, Soul, Dreams, Shadows, Light
- Action words: Journey, Quest, Dance, Whisper, Shatter
- Atmospheric words: Midnight, Dawn, Storm, Silence, Echo

Return ONLY a valid JSON array with objects containing:
- id: unique identifier
- text: the creative title
- style: one of "evocative", "symbolic", "character-driven", "thematic"
- emotionalTone: primary emotional appeal
- genreAppeal: how this title fits the genre

JSON Response:`;
  }

  if (promptType === 'introduction') {
    return `You are an expert creative writer specializing in ${subType || 'creative writing'}. Write a captivating introduction for your creative work:

Title: ${baseContext.title}
Topic/Theme: ${baseContext.topic}
Target Audience: ${baseContext.audience}
Creative Genre: ${subType || 'Creative Writing'}
Voice: ${requirements.voicePreference || 'First-person narrative'}

CREATIVE INTRODUCTION REQUIREMENTS:
- Hook readers immediately with compelling opening scene or voice
- Establish the narrative voice and tone from the first sentence
- Introduce key characters or central concept naturally
- Set the mood and atmosphere for the entire work
- Create immediate emotional connection with readers
- Establish the world, setting, or context effectively
- Build intrigue and questions that compel continued reading

INTRODUCTION STRUCTURE:
- Powerful opening hook (dialogue, action, or vivid scene)
- Character or narrator voice establishment
- Setting and atmosphere creation
- Central conflict or tension introduction
- Emotional stakes establishment
- Reader engagement and curiosity building

CREATIVE TECHNIQUES:
- Use vivid, sensory descriptions that immerse readers
- Include authentic dialogue that reveals character
- Create immediate tension or intrigue
- Show rather than tell important information
- Use literary devices appropriate to your genre
- Establish consistent point of view and tense

Length: 500-800 words
Tone: ${baseContext.tone || 'Engaging, immersive, emotionally resonant'}

Generate the complete creative introduction in markdown format:`;
  }

  if (promptType === 'chapter' && chapterData) {
    return `You are an expert creative writer and storyteller. Generate compelling content for this creative section:

Chapter ${chapterData.number}: ${chapterData.title}

CREATIVE CONTEXT:
- Document Type: Creative ${subType || 'Writing'}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Creative Style: ${requirements.writingStyle || 'Engaging narrative'}
- Tone: ${baseContext.tone || 'Creative and engaging'}
- Voice: ${requirements.voicePreference || 'First-person narrative'}

CHAPTER SECTIONS TO COVER:
${chapterData.sections?.map(section => `- ${section}`).join('\n') || '- Main creative content for this chapter'}

CREATIVE WRITING REQUIREMENTS:
- Use vivid, descriptive language that engages the senses
- Develop compelling characters, scenes, or concepts
- Maintain consistent voice and style throughout
- Include dialogue, imagery, and narrative techniques
- Create emotional connection with readers
- Use creative formatting and structure when appropriate
- Balance creativity with clarity and readability
- Build tension and advance the plot or theme

CONTENT STRUCTURE:
- Begin with engaging hook or scene setting
- Develop content with rich descriptions and narrative flow
- Include character development or concept exploration
- Use creative transitions and pacing
- Build toward chapter climax or revelation
- Conclude with satisfying resolution or cliffhanger
- Maintain consistent creative voice throughout

CREATIVE TECHNIQUES:
- Show don't tell through action and dialogue
- Use literary devices (metaphor, symbolism, foreshadowing)
- Create authentic character voices and interactions
- Build atmosphere through sensory details
- Maintain appropriate pacing for your genre
- Include conflict and tension to drive narrative forward

LENGTH: 800-1200 words
TONE: Creative, engaging, imaginative, emotionally resonant

Generate the complete creative chapter content in markdown format:`;
  }

  if (promptType === 'conclusion') {
    return `You are an expert creative writer and storytelling coach. Write a powerful conclusion for your ${subType || 'creative work'}:

Title: ${baseContext.title}
Topic/Theme: ${baseContext.topic}
Target Audience: ${baseContext.audience}
Creative Genre: ${subType || 'Creative Writing'}
Voice: ${requirements.voicePreference || 'First-person narrative'}

CREATIVE CONCLUSION REQUIREMENTS:
- Provide satisfying resolution to central conflicts and themes
- Bring character arcs to meaningful completion
- Resolve plot threads while maintaining emotional impact
- Reinforce central themes through final scenes or reflections
- Create lasting emotional resonance with readers
- Use language and imagery that echoes opening themes
- Leave readers with sense of completion and satisfaction

CONCLUSION STRUCTURE:
- Climactic resolution of central conflict
- Character growth and transformation demonstration
- Thematic reinforcement through action or reflection
- Emotional catharsis or meaningful revelation
- Tying together of narrative threads
- Final image or thought that resonates

CREATIVE TECHNIQUES:
- Use callback to opening themes, images, or phrases
- Show character transformation through action, not exposition
- Create emotional catharsis appropriate to your genre
- Use vivid, memorable final imagery
- Maintain consistent voice and tone to the end
- Balance resolution with appropriate ambiguity

Length: 400-800 words
Tone: ${baseContext.tone || 'Emotionally resonant, satisfying, memorable'}

Generate the complete creative conclusion in markdown format:`;
  }

  return `Creative ${promptType} prompt for ${subType} - to be implemented`;
};

// Report functionality removed - only supporting ebook, academic, business, guide

/**
 * Create eBook document prompts (fiction, non-fiction, self-help, educational)
 * @param {Object} documentData - Document configuration
 * @param {Object} requirements - eBook requirements
 * @param {string} subType - eBook sub-type (fiction, non-fiction, self-help, etc.)
 * @param {string} promptType - Type of prompt to generate
 * @param {Object} chapterData - Chapter-specific data for content generation
 * @returns {string} eBook-specific AI prompt
 */
const createEbookPrompt = (documentData, requirements, subType, promptType, chapterData) => {
  const baseContext = extractBaseContext(documentData);
  const ebookSections = getEbookSections(subType);

  if (promptType === 'outline') {
    return `You are an expert eBook author and publishing strategist specializing in ${baseContext.topic}. Create a comprehensive ${subType || 'eBook'} outline that will engage readers and deliver exceptional value.

EBOOK SPECIFICATIONS:
- Type: ${subType || 'eBook'}
- Title: ${baseContext.title}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Publishing Format: Digital (PDF, ePub, Kindle)
- Reading Experience: ${getEbookReadingContext(subType)}

EBOOK REQUIREMENTS:
- Narrative Structure: ${requirements.narrativeStructure ? 'Strong story arc with compelling progression' : 'Logical information flow'}
- Chapter Organization: Sequential chapters that build upon each other
- Reader Engagement: High engagement with hooks, cliffhangers, and compelling content
- Publishing Considerations: Optimized for digital reading with proper formatting
- Table of Contents: Clear navigation structure for digital readers
- Target Length: ${getEbookTargetLength(subType)}

REQUIRED EBOOK SECTIONS:
${ebookSections.map(section => `- ${section.name}: ${section.description} (${section.wordCount})`).join('\n')}

EBOOK WRITING STANDARDS:
- Create compelling chapter titles that entice readers to continue
- Develop strong opening hooks and satisfying chapter conclusions
- Maintain consistent voice and tone throughout the book
- Include practical examples, stories, or case studies where appropriate
- Design content for optimal digital reading experience
- Consider chapter length for comfortable reading sessions (2000-4000 words)
- Include clear transitions between chapters and sections
- Optimize for searchability and digital bookmarking

READER ENGAGEMENT TECHNIQUES:
- Start each chapter with a compelling hook or question
- Use storytelling techniques to illustrate key points
- Include actionable takeaways and practical applications
- Create emotional connection with readers through relatable examples
- End chapters with curiosity gaps or cliffhangers when appropriate
- Use formatting that enhances digital reading (headers, bullet points, etc.)

PUBLISHING FORMAT CONSIDERATIONS:
- Structure content for easy navigation in digital formats
- Consider how content will appear on various devices (phones, tablets, e-readers)
- Include appropriate chapter breaks and section divisions
- Plan for interactive elements where applicable (links, references)

Return ONLY a valid JSON object with this structure:
{
  "title": "Compelling eBook title",
  "subtitle": "Engaging subtitle that clarifies value proposition",
  "description": "Brief description of what readers will gain (100-200 words)",
  "targetAudience": "Specific reader demographics and interests",
  "chapters": [
    {
      "number": 1,
      "title": "Engaging chapter title",
      "sections": ["Section 1", "Section 2", "Section 3"],
      "wordCount": "estimated word count",
      "chapterPurpose": "what this chapter accomplishes for the reader",
      "keyTakeaways": ["main learning point 1", "main learning point 2"],
      "engagementHook": "how this chapter hooks the reader"
    }
  ],
  "requiredElements": ["table of contents", "introduction", "conclusion", "about the author"],
  "ebookType": "${subType || 'general'}",
  "readingLevel": "appropriate reading level",
  "estimatedReadingTime": "total reading time",
  "publishingFormat": "digital optimization notes"
}

JSON Response:`;
  }

  if (promptType === 'title') {
    return `You are an expert eBook author and digital publishing strategist. Generate 8 compelling eBook titles for a ${subType || 'eBook'}:

Topic: ${baseContext.topic}
Target Audience: ${baseContext.audience}
eBook Purpose: ${getEbookPurpose(subType)}
Reading Context: ${getEbookReadingContext(subType)}

EBOOK TITLE REQUIREMENTS:
- Create titles that stand out in digital marketplaces (Amazon, Apple Books, etc.)
- Use emotional triggers and benefit-driven language
- Include power words that convey transformation and value
- Length: 3-8 words optimal for digital display and searchability
- Consider SEO and discoverability in digital bookstores
- Appeal to the specific motivations of your target audience
- Avoid overly generic or clichéd phrases

TITLE STYLES TO INCLUDE:
- Benefit-Driven: Focus on what readers will gain or achieve
- Problem-Solution: Address specific pain points and solutions
- Transformation: Emphasize change and improvement
- Authority: Position as definitive guide or expert resource

EBOOK POWER WORDS TO CONSIDER:
- Complete, Ultimate, Essential, Proven, Secret, Mastery
- Transform, Discover, Unlock, Master, Achieve, Success
- Guide, Blueprint, System, Method, Strategy, Formula
- ${getEbookKeywords(subType)}

DIGITAL PUBLISHING CONSIDERATIONS:
- Titles should be compelling in thumbnail view
- Consider how titles appear in search results
- Think about subtitle opportunities for clarification
- Ensure titles work across different digital platforms

Return ONLY a valid JSON array with objects containing:
- id: unique identifier
- text: the eBook title
- style: one of "benefit-driven", "problem-solution", "transformation", "authority"
- marketingHook: primary appeal to target audience
- digitalOptimization: how this title performs in digital marketplaces

JSON Response:`;
  }

  if (promptType === 'introduction') {
    return `You are an expert eBook author specializing in ${subType || 'digital publishing'}. Write a compelling introduction for your eBook:

Title: ${baseContext.title}
Topic: ${baseContext.topic}
Target Audience: ${baseContext.audience}
eBook Type: ${subType || 'General eBook'}

EBOOK INTRODUCTION REQUIREMENTS:
- Hook readers immediately with a compelling opening
- Clearly establish the value proposition and what readers will gain
- Create emotional connection with your target audience
- Set expectations for the reading journey ahead
- Include personal credibility or story if appropriate
- Address the specific problem or opportunity your book solves
- Create excitement and anticipation for the content to come

INTRODUCTION STRUCTURE:
- Opening hook that captures attention immediately
- Problem identification that resonates with readers
- Promise of transformation or solution
- Brief overview of what's covered (without spoilers)
- Personal connection or credibility establishment
- Call to action to continue reading

ENGAGEMENT TECHNIQUES:
- Use storytelling to illustrate key points
- Include relatable scenarios or examples
- Ask thought-provoking questions
- Create curiosity gaps that compel continued reading
- Use conversational, accessible language
- Build anticipation for the value to come

Length: 500-800 words
Tone: ${getEbookTone(subType)}

Generate the complete eBook introduction in markdown format:`;
  }

  if (promptType === 'chapter' && chapterData) {
    return `You are an expert eBook author and content strategist. Generate compelling content for this eBook chapter:

Chapter ${chapterData.number}: ${chapterData.title}

EBOOK CONTEXT:
- Document Type: ${subType || 'eBook'}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Reading Experience: ${getEbookReadingContext(subType)}
- Chapter Purpose: ${chapterData.chapterPurpose || 'Advance the reader\'s understanding and engagement'}

CHAPTER SECTIONS TO COVER:
${chapterData.sections?.map(section => `- ${section}`).join('\n') || '- Main chapter content'}

KEY TAKEAWAYS FOR THIS CHAPTER:
${chapterData.keyTakeaways?.map(takeaway => `- ${takeaway}`).join('\n') || '- Primary learning objectives'}

EBOOK WRITING REQUIREMENTS:
- Create engaging, readable content optimized for digital consumption
- Use storytelling techniques and real-world examples
- Maintain consistent voice and tone throughout
- Include practical applications and actionable insights
- Design content for comfortable digital reading sessions
- Use formatting that enhances readability (headers, bullet points, etc.)
- Create smooth transitions between sections
- End with compelling hooks or cliffhangers when appropriate

CONTENT STRUCTURE:
- Begin with engaging hook or chapter preview
- Develop content with clear progression and logical flow
- Include practical examples, case studies, or stories
- Provide actionable takeaways and implementation guidance
- Use subheadings and formatting for easy scanning
- Conclude with chapter summary and transition to next chapter

READER ENGAGEMENT TECHNIQUES:
- Use conversational, accessible language
- Include relatable examples and scenarios
- Ask rhetorical questions to maintain engagement
- Create emotional connection through storytelling
- Provide immediate value and practical insights
- Use formatting to break up text and improve readability

DIGITAL READING OPTIMIZATION:
- Structure content for various screen sizes
- Use appropriate paragraph lengths for digital reading
- Include clear section breaks and transitions
- Consider how content flows on different devices
- Optimize for both linear and non-linear reading patterns

LENGTH: 2000-4000 words (optimal for digital reading sessions)
TONE: ${getEbookTone(subType)}

Generate the complete eBook chapter content in markdown format:`;
  }

  if (promptType === 'conclusion') {
    return `You are an expert eBook author and publishing strategist. Write a powerful conclusion for your ${subType || 'eBook'}:

Title: ${baseContext.title}
Topic: ${baseContext.topic}
Target Audience: ${baseContext.audience}
eBook Type: ${subType || 'General eBook'}
Reading Experience: ${getEbookReadingContext(subType)}

EBOOK CONCLUSION REQUIREMENTS:
- Provide satisfying closure to the reader's journey
- Reinforce key takeaways and value delivered
- Inspire readers to take action on what they've learned
- Include practical next steps for continued growth
- Create emotional connection and lasting impact
- Encourage readers to share or recommend the book
- End with memorable final thought or call-to-action

CONCLUSION STRUCTURE:
- Acknowledgment of reader's commitment and progress
- Summary of key insights and transformations achieved
- Reinforcement of main value propositions
- Practical next steps and implementation guidance
- Additional resources for continued learning
- Inspirational closing message
- Clear call-to-action for engagement

READER ENGAGEMENT TECHNIQUES:
- Use personal, conversational language that connects
- Include success stories or transformation examples
- Provide specific, actionable next steps
- Create sense of community and ongoing support
- Use motivational language that inspires action
- Include ways for readers to stay connected

DIGITAL PUBLISHING CONSIDERATIONS:
- Include links to additional resources where appropriate
- Consider follow-up content or sequel opportunities
- Encourage reviews and social sharing
- Provide contact information for further engagement
- Optimize for digital reading completion satisfaction

Length: 500-800 words
Tone: ${getEbookTone(subType)}, inspirational, actionable

Generate the complete eBook conclusion in markdown format:`;
  }

  // Handle other prompt types for eBook documents
  return createEbookContentPrompt(documentData, requirements, subType, promptType);
};

/**
 * Get eBook section structure based on sub-type
 * @param {string} subType - eBook document sub-type
 * @returns {Array} Array of section objects with eBook requirements
 */
const getEbookSections = (subType) => {
  const sections = {
    'fiction': [
      { name: 'Prologue/Opening', description: 'Compelling opening that hooks readers immediately', wordCount: '1000-2000 words' },
      { name: 'Character Introduction', description: 'Introduce main characters and establish setting', wordCount: '2000-3000 words' },
      { name: 'Rising Action', description: 'Build tension and develop plot with engaging scenes', wordCount: '15000-25000 words' },
      { name: 'Climax', description: 'Peak dramatic moment and turning point', wordCount: '3000-5000 words' },
      { name: 'Falling Action', description: 'Resolution of conflicts and loose ends', wordCount: '2000-4000 words' },
      { name: 'Conclusion/Epilogue', description: 'Satisfying ending that provides closure', wordCount: '1000-2000 words' }
    ],
    'non-fiction': [
      { name: 'Introduction', description: 'Hook readers and establish value proposition', wordCount: '1000-1500 words' },
      { name: 'Foundation Concepts', description: 'Essential background knowledge and principles', wordCount: '3000-5000 words' },
      { name: 'Core Content Chapters', description: 'Main educational or informational content', wordCount: '20000-35000 words' },
      { name: 'Practical Applications', description: 'Real-world examples and implementation guidance', wordCount: '3000-5000 words' },
      { name: 'Advanced Concepts', description: 'Deeper insights and sophisticated applications', wordCount: '3000-5000 words' },
      { name: 'Conclusion & Next Steps', description: 'Summary and guidance for continued learning', wordCount: '1000-2000 words' }
    ],
    'self-help': [
      { name: 'Personal Story/Hook', description: 'Compelling personal narrative that establishes credibility', wordCount: '1500-2500 words' },
      { name: 'Problem Identification', description: 'Clearly define the challenge readers face', wordCount: '2000-3000 words' },
      { name: 'Solution Framework', description: 'Introduce your methodology or approach', wordCount: '2000-3000 words' },
      { name: 'Step-by-Step Implementation', description: 'Detailed guidance with practical exercises', wordCount: '15000-25000 words' },
      { name: 'Overcoming Obstacles', description: 'Address common challenges and setbacks', wordCount: '3000-4000 words' },
      { name: 'Transformation Stories', description: 'Success stories and case studies', wordCount: '2000-3000 words' },
      { name: 'Maintaining Progress', description: 'Long-term strategies for sustained success', wordCount: '2000-3000 words' }
    ],
    'educational': [
      { name: 'Learning Objectives', description: 'Clear goals and expectations for readers', wordCount: '500-1000 words' },
      { name: 'Foundational Knowledge', description: 'Essential concepts and terminology', wordCount: '3000-5000 words' },
      { name: 'Core Curriculum', description: 'Main educational content organized by topics', wordCount: '20000-40000 words' },
      { name: 'Practical Exercises', description: 'Hands-on activities and practice problems', wordCount: '3000-5000 words' },
      { name: 'Case Studies', description: 'Real-world applications and examples', wordCount: '3000-5000 words' },
      { name: 'Assessment & Review', description: 'Self-testing and knowledge reinforcement', wordCount: '2000-3000 words' },
      { name: 'Further Resources', description: 'Additional learning materials and references', wordCount: '1000-2000 words' }
    ],
    'biography': [
      { name: 'Early Life', description: 'Background, childhood, and formative experiences', wordCount: '3000-5000 words' },
      { name: 'Defining Moments', description: 'Key events that shaped the subject\'s path', wordCount: '4000-6000 words' },
      { name: 'Career/Life Journey', description: 'Professional development and major achievements', wordCount: '10000-20000 words' },
      { name: 'Challenges & Obstacles', description: 'Difficulties faced and how they were overcome', wordCount: '3000-5000 words' },
      { name: 'Relationships & Influences', description: 'Important people and their impact', wordCount: '3000-5000 words' },
      { name: 'Legacy & Impact', description: 'Lasting contributions and influence', wordCount: '2000-4000 words' }
    ],
    'reference': [
      { name: 'How to Use This Book', description: 'Navigation guide and reference instructions', wordCount: '500-1000 words' },
      { name: 'Quick Reference Guide', description: 'Essential information for immediate access', wordCount: '2000-3000 words' },
      { name: 'Comprehensive Entries', description: 'Detailed reference material organized systematically', wordCount: '20000-50000 words' },
      { name: 'Cross-References', description: 'Connections between related topics and concepts', wordCount: '1000-2000 words' },
      { name: 'Appendices', description: 'Additional resources, charts, and supplementary material', wordCount: '3000-5000 words' },
      { name: 'Index', description: 'Searchable index for easy information retrieval', wordCount: '1000-2000 words' }
    ],
    'children': [
      { name: 'Engaging Opening', description: 'Fun, colorful introduction that captures young attention', wordCount: '200-500 words' },
      { name: 'Story Development', description: 'Age-appropriate narrative with clear progression', wordCount: '1000-3000 words' },
      { name: 'Interactive Elements', description: 'Questions, activities, or participation opportunities', wordCount: '500-1000 words' },
      { name: 'Educational Content', description: 'Learning objectives woven into entertaining format', wordCount: '1000-2000 words' },
      { name: 'Visual Descriptions', description: 'Rich descriptions that support illustrations', wordCount: '500-1000 words' },
      { name: 'Satisfying Conclusion', description: 'Positive ending with clear resolution', wordCount: '200-500 words' }
    ]
  };

  return sections[subType] || sections['non-fiction'];
};

/**
 * Get eBook reading context based on sub-type
 * @param {string} subType - eBook sub-type
 * @returns {string} Reading context description
 */
const getEbookReadingContext = (subType) => {
  const contexts = {
    'fiction': 'Entertainment and escapism through compelling storytelling',
    'non-fiction': 'Education and information acquisition for personal or professional growth',
    'self-help': 'Personal transformation and improvement through actionable guidance',
    'educational': 'Structured learning and skill development with clear objectives',
    'biography': 'Inspiration and insight through life stories and experiences',
    'reference': 'Quick information lookup and comprehensive resource consultation',
    'children': 'Fun, engaging learning experience appropriate for young readers'
  };

  return contexts[subType] || 'Engaging digital reading experience with valuable content';
};

/**
 * Get eBook purpose based on sub-type
 * @param {string} subType - eBook sub-type
 * @returns {string} Purpose description
 */
const getEbookPurpose = (subType) => {
  const purposes = {
    'fiction': 'Entertainment, emotional engagement, and storytelling excellence',
    'non-fiction': 'Education, information sharing, and knowledge transfer',
    'self-help': 'Personal development, transformation, and actionable improvement',
    'educational': 'Structured learning, skill building, and academic achievement',
    'biography': 'Inspiration, historical insight, and personal story sharing',
    'reference': 'Information access, problem-solving, and comprehensive guidance',
    'children': 'Age-appropriate learning, entertainment, and development'
  };

  return purposes[subType] || 'Valuable content delivery and reader engagement';
};

/**
 * Get eBook target length based on sub-type
 * @param {string} subType - eBook sub-type
 * @returns {string} Target length description
 */
const getEbookTargetLength = (subType) => {
  const lengths = {
    'fiction': '50,000-100,000 words (novel length)',
    'non-fiction': '25,000-60,000 words (comprehensive but accessible)',
    'self-help': '30,000-50,000 words (detailed guidance with examples)',
    'educational': '40,000-80,000 words (thorough coverage of subject matter)',
    'biography': '60,000-100,000 words (comprehensive life story)',
    'reference': '30,000-100,000 words (comprehensive reference material)',
    'children': '500-5,000 words (age-appropriate length)'
  };

  return lengths[subType] || '25,000-50,000 words (standard eBook length)';
};

/**
 * Get eBook tone based on sub-type
 * @param {string} subType - eBook sub-type
 * @returns {string} Tone description
 */
const getEbookTone = (subType) => {
  const tones = {
    'fiction': 'Engaging, immersive, emotionally resonant, narrative-driven',
    'non-fiction': 'Informative, authoritative, accessible, professional',
    'self-help': 'Encouraging, supportive, motivational, practical',
    'educational': 'Clear, instructional, comprehensive, student-friendly',
    'biography': 'Respectful, insightful, engaging, historically accurate',
    'reference': 'Precise, comprehensive, organized, user-friendly',
    'children': 'Fun, age-appropriate, engaging, positive'
  };

  return tones[subType] || 'Engaging, informative, reader-focused';
};

/**
 * Get eBook keywords based on sub-type
 * @param {string} subType - eBook sub-type
 * @returns {string} Relevant keywords
 */
const getEbookKeywords = (subType) => {
  const keywords = {
    'fiction': 'Story, Novel, Adventure, Romance, Mystery, Thriller',
    'non-fiction': 'Learn, Understand, Discover, Explore, Master, Knowledge',
    'self-help': 'Transform, Improve, Change, Success, Growth, Breakthrough',
    'educational': 'Learn, Study, Master, Course, Training, Skills',
    'biography': 'Life, Story, Journey, Legacy, Inspiration, History',
    'reference': 'Handbook, Guide, Resource, Encyclopedia, Directory, Compendium',
    'children': 'Fun, Adventure, Learn, Discover, Friends, Magic'
  };

  return keywords[subType] || 'Guide, Learn, Discover, Master, Success';
};

/**
 * Create eBook content prompts for specific sections
 * @param {Object} documentData - Document configuration
 * @param {Object} requirements - eBook requirements
 * @param {string} subType - eBook sub-type
 * @param {string} promptType - Specific prompt type
 * @returns {string} eBook content prompt
 */
const createEbookContentPrompt = (documentData, requirements, subType, promptType) => {
  const baseContext = extractBaseContext(documentData);

  // Add more eBook prompt types as needed
  return `eBook ${promptType} prompt for ${subType} - to be implemented with specialized content for ${baseContext.topic}`;
};

/**
 * Helper functions for Creative Writing prompts
 */
const getCreativeStyle = (subType) => {
  const styles = {
    'short-story': 'Narrative fiction with character development and plot progression',
    'screenplay': 'Script format with dialogue, action, and scene descriptions',
    'memoir': 'Personal narrative with reflective and introspective tone',
    'poetry': 'Lyrical expression with rhythm, imagery, and emotional resonance',
    'creative-nonfiction': 'Factual content presented with narrative techniques'
  };
  return styles[subType] || 'Engaging narrative with creative expression';
};

const getCharacterRequirements = (subType) => {
  const requirements = {
    'short-story': 'Well-developed protagonist with clear motivation and character arc',
    'screenplay': 'Distinct character voices with visual and behavioral traits',
    'memoir': 'Authentic personal voice with honest self-reflection',
    'poetry': 'Persona or voice that carries emotional weight and meaning',
    'creative-nonfiction': 'Real people presented with depth and complexity'
  };
  return requirements[subType] || 'Compelling characters with authentic voices';
};

const getPlotStructure = (subType) => {
  const structures = {
    'short-story': 'Classic story arc with setup, conflict, climax, and resolution',
    'screenplay': 'Three-act structure with visual storytelling and scene progression',
    'memoir': 'Thematic organization around key life events and insights',
    'poetry': 'Emotional or thematic progression through verses or stanzas',
    'creative-nonfiction': 'Narrative structure that serves factual content'
  };
  return structures[subType] || 'Clear beginning, middle, and end with satisfying progression';
};

const getCreativeSections = (subType) => {
  const sections = {
    'short-story': [
      { name: 'Opening Hook', description: 'Compelling opening that draws readers in', wordCount: '200-500 words' },
      { name: 'Character Introduction', description: 'Introduce protagonist and establish voice', wordCount: '300-600 words' },
      { name: 'Rising Action', description: 'Build tension and develop conflict', wordCount: '1000-2000 words' },
      { name: 'Climax', description: 'Peak moment of tension and revelation', wordCount: '300-600 words' },
      { name: 'Resolution', description: 'Satisfying conclusion and character growth', wordCount: '200-500 words' }
    ],
    'screenplay': [
      { name: 'Act I - Setup', description: 'Character introduction and inciting incident', wordCount: '20-25 pages' },
      { name: 'Act II - Confrontation', description: 'Rising action and character development', wordCount: '50-60 pages' },
      { name: 'Act III - Resolution', description: 'Climax and resolution', wordCount: '20-25 pages' }
    ],
    'memoir': [
      { name: 'Early Memories', description: 'Formative experiences and background', wordCount: '2000-4000 words' },
      { name: 'Defining Moments', description: 'Key events that shaped your perspective', wordCount: '3000-6000 words' },
      { name: 'Challenges & Growth', description: 'Obstacles overcome and lessons learned', wordCount: '3000-6000 words' },
      { name: 'Reflections', description: 'Current perspective and wisdom gained', wordCount: '1000-3000 words' }
    ]
  };
  return sections[subType] || sections['short-story'];
};

const getCreativeLength = (subType) => {
  const lengths = {
    'short-story': '2,000-7,500 words',
    'screenplay': '90-120 pages (script format)',
    'memoir': '60,000-90,000 words',
    'poetry': '48-100 pages (collection)',
    'creative-nonfiction': '50,000-80,000 words'
  };
  return lengths[subType] || '5,000-15,000 words';
};

const getCreativePurpose = (subType) => {
  const purposes = {
    'short-story': 'Entertainment and emotional engagement through narrative',
    'screenplay': 'Visual storytelling for film or television production',
    'memoir': 'Personal story sharing and life lesson communication',
    'poetry': 'Emotional expression and artistic language exploration',
    'creative-nonfiction': 'Factual information presented through creative narrative'
  };
  return purposes[subType] || 'Creative expression and reader engagement';
};

const getCreativeContext = (subType) => {
  const contexts = {
    'short-story': 'Literary fiction market with focus on character and theme',
    'screenplay': 'Film and television industry with visual storytelling emphasis',
    'memoir': 'Personal narrative market with authentic voice requirement',
    'poetry': 'Literary poetry community with artistic expression focus',
    'creative-nonfiction': 'Narrative nonfiction market combining facts with story'
  };
  return contexts[subType] || 'Creative writing community with artistic expression focus';
};

const getCreativeKeywords = (subType) => {
  const keywords = {
    'short-story': 'Story, Tale, Journey, Character, Emotion, Drama',
    'screenplay': 'Script, Scene, Action, Dialogue, Visual, Cinema',
    'memoir': 'Life, Memory, Journey, Truth, Experience, Reflection',
    'poetry': 'Verse, Voice, Rhythm, Image, Soul, Expression',
    'creative-nonfiction': 'Truth, Story, Reality, Narrative, Experience, Insight'
  };
  return keywords[subType] || 'Story, Voice, Journey, Truth, Expression, Art';
};

// Report helper functions removed - only supporting ebook, academic, business, guide






